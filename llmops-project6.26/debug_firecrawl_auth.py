#!/usr/bin/env python3
"""
调试firecrawl插件授权表单问题
"""

import requests
import json
import sys
import urllib.parse

def debug_firecrawl_auth():
    """调试firecrawl插件授权表单问题"""
    
    # 配置
    base_url = "http://172.16.201.93:30080"
    auth_token = "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5MDk5OTEsImlhdCI6MTY1NjMwOTk5MSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.G9bUZ984g-8M-yBuUisrW9HtIK-yP0G-tD4DW9VQ7xAvmyMRaTOJ9DlucZj7KK_ynES1Qa96HZV_Wz5KlUUWtA"
    
    headers = {
        'Authorization': auth_token,
        'Content-Type': 'application/json'
    }
    
    print("🔍 调试firecrawl插件授权表单问题")
    print("=" * 60)
    
    # 1. 获取插件列表，查找firecrawl相关插件
    print("\n📋 步骤1: 查找firecrawl相关插件")
    
    try:
        response = requests.get(f"{base_url}/api/v1/app/dify-plugins/plugins", headers=headers, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            plugins = data.get('plugins', [])
            print(f"✅ 成功获取插件列表，共 {len(plugins)} 个插件")
            
            firecrawl_plugins = []
            for plugin in plugins:
                plugin_id = plugin.get('plugin_id', '')
                name = plugin.get('name', '')
                unique_id = plugin.get('unique_identifier', '')
                
                if 'firecrawl' in plugin_id.lower() or 'firecrawl' in name.lower():
                    firecrawl_plugins.append(plugin)
                    print(f"\n找到firecrawl插件:")
                    print(f"  plugin_id: '{plugin_id}'")
                    print(f"  name: '{name}'")
                    print(f"  unique_identifier: '{unique_id}'")
                    print(f"  source: {plugin.get('source', 'Unknown')}")
            
            if not firecrawl_plugins:
                print("❌ 未找到firecrawl相关插件")
                return False
                
        else:
            print(f"❌ 获取插件列表失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False
    
    # 2. 测试firecrawl插件详情获取
    print(f"\n📄 步骤2: 测试firecrawl插件详情获取")
    
    test_ids = ['firecrawl', 'langgenius/firecrawl', 'Firecrawl']
    
    for test_id in test_ids:
        print(f"\n测试ID: '{test_id}'")
        
        try:
            encoded_id = urllib.parse.quote(test_id, safe='')
            detail_response = requests.get(
                f"{base_url}/api/v1/app/dify-plugins/plugins/{encoded_id}", 
                headers=headers, 
                timeout=15
            )
            
            print(f"  状态码: {detail_response.status_code}")
            
            if detail_response.status_code == 200:
                detail_data = detail_response.json()
                print(f"  ✅ 成功获取插件详情")
                print(f"  plugin_id: {detail_data.get('plugin_id', 'Unknown')}")
                print(f"  unique_identifier: {detail_data.get('unique_identifier', 'Unknown')}")
                print(f"  name: {detail_data.get('name', 'Unknown')}")
                
                # 测试授权表单
                print(f"  测试授权表单...")
                auth_response = requests.get(
                    f"{base_url}/api/v1/app/dify-plugins/plugins/{encoded_id}/auth/form", 
                    headers=headers, 
                    timeout=15
                )
                
                print(f"  授权表单状态码: {auth_response.status_code}")
                
                if auth_response.status_code == 200:
                    auth_data = auth_response.json()
                    fields = auth_data.get('fields', [])
                    print(f"  ✅ 成功获取授权表单，字段数量: {len(fields)}")
                    
                    for field in fields:
                        print(f"    - {field.get('name', 'Unknown')}: {field.get('label', 'No label')}")
                else:
                    print(f"  ❌ 授权表单获取失败: {auth_response.text[:100]}...")
                    
            elif detail_response.status_code == 404:
                print(f"  ❌ 插件不存在")
            else:
                print(f"  ❌ 其他错误: {detail_response.text[:100]}...")
                
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")
    
    # 3. 直接测试Dify API
    print(f"\n🔗 步骤3: 直接测试Dify API")
    
    if firecrawl_plugins:
        plugin = firecrawl_plugins[0]
        unique_identifier = plugin.get('unique_identifier', '')
        
        print(f"测试插件: {plugin.get('name', 'Unknown')}")
        print(f"unique_identifier: '{unique_identifier}'")
        
        if unique_identifier:
            try:
                # 直接调用Dify的fetch-manifest API
                dify_base_url = "http://************"
                dify_auth_token = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMGI2N2Y3ZjYtMjQ5OC00ZjM0LTk0ZDEtMmMwZmI0NDVmMDg0IiwiZXhwIjoxNzg0OTQ4NzE1LCJpc3MiOiJTRUxGX0hPU1RFRCIsInN1YiI6IkNvbnNvbGUgQVBJIFBhc3Nwb3J0In0.HWPgwNIp0ux1ii9AAcM7CSxKXiTQ3I2r63uhd8FWuxI"
                
                dify_headers = {
                    'Authorization': dify_auth_token,
                    'Content-Type': 'application/json'
                }
                
                manifest_url = f"{dify_base_url}/console/api/workspaces/current/plugin/fetch-manifest"
                params = {'plugin_unique_identifier': unique_identifier}
                
                print(f"调用Dify API: {manifest_url}")
                print(f"参数: {params}")
                
                manifest_response = requests.get(manifest_url, headers=dify_headers, params=params, timeout=30)
                
                print(f"Dify API状态码: {manifest_response.status_code}")
                
                if manifest_response.status_code == 200:
                    manifest_data = manifest_response.json()
                    manifest = manifest_data.get('manifest', {})
                    
                    print(f"✅ 成功获取manifest")
                    print(f"manifest keys: {list(manifest.keys())}")
                    
                    credentials = manifest.get('credentials', {})
                    print(f"credentials: {credentials}")
                    
                    if credentials:
                        print("授权字段:")
                        for field_name, field_config in credentials.items():
                            print(f"  - {field_name}: {field_config}")
                    else:
                        print("⚠️  没有找到credentials配置")
                        
                else:
                    print(f"❌ Dify API调用失败: {manifest_response.text[:200]}...")
                    
            except Exception as e:
                print(f"❌ Dify API调用异常: {e}")
    
    # 4. 测试删除的插件
    print(f"\n🗑️  步骤4: 测试删除的插件")
    
    deleted_plugin_id = "google"
    print(f"测试删除的插件: {deleted_plugin_id}")
    
    try:
        auth_response = requests.get(
            f"{base_url}/api/v1/app/dify-plugins/plugins/{deleted_plugin_id}/auth/form", 
            headers=headers, 
            timeout=15
        )
        
        print(f"状态码: {auth_response.status_code}")
        print(f"响应: {auth_response.text}")
        
        if auth_response.status_code == 404:
            print("✅ 正确返回404 - 插件不存在")
        elif auth_response.status_code == 200:
            auth_data = auth_response.json()
            if 'fields' in auth_data and len(auth_data['fields']) == 0:
                print("⚠️  返回了空字段，但应该返回错误")
            else:
                print("❌ 不应该返回授权字段")
        else:
            print(f"⚠️  其他响应")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    print(f"\n" + "=" * 60)
    print("🎯 分析结论:")
    print("1. 检查firecrawl插件的unique_identifier是否正确")
    print("2. 验证Dify API调用是否成功")
    print("3. 确认删除插件的错误处理是否正确")

def main():
    """主函数"""
    debug_firecrawl_auth()
    return 0

if __name__ == "__main__":
    sys.exit(main())
