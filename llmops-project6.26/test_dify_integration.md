# Dify插件集成测试文档

## 🎯 功能概述

在现有的工具分类（REST、MCP、Dynamic MCP）基础上，新增了"Dify"类别，用于展示从Dify系统获取的插件。

## 🔧 实现的修改

### 1. 扩展ServerType定义
```go
// 在 agent_definition/api_tool.go 中添加
const (
    ServerTypeRest       ServerType = "rest"
    ServerTypeMCP        ServerType = "mcp"
    ServerTypeDynamicMCP ServerType = "dynamic_mcp"
    ServerTypeDify       ServerType = "dify"  // 新增
)
```

### 2. 添加Dify插件获取接口
```go
// 在 IAPITool 接口中添加
GetDifyPlugins(ctx context.Context) ([]*api_tools.APIToolCollectionBaseDO, error)
```

### 3. 实现GetDifyPlugins方法
- 调用 `http://localhost:8080/v1/dify-plugins/installed`
- 解析返回的Dify插件数据
- 转换为标准的APIToolCollectionBaseDO格式
- 设置ServerType为"dify"

### 4. 集成到工具列表
在`ListToolCollectionBaseInfos`方法中添加Dify插件的获取和合并逻辑。

## 📊 数据转换

### 输入数据格式（来自agent-tool-api）
```json
{
    "success": true,
    "data": [
        {
            "provider_name": "siliconflow",
            "plugin_unique_identifier": "langgenius/siliconflow:0.0.20@...",
            "plugin_name": "siliconflow",
            "plugin_description": "硅基流动提供对各种模型的访问...",
            "plugin_version": "0.0.20",
            "auth_status": "no_auth_required"
        }
    ]
}
```

### 输出数据格式（转换为工具集合格式）
```json
{
    "id": "dify-plugin-siliconflow",
    "name": "siliconflow",
    "desc": "硅基流动提供对各种模型的访问...",
    "api_tool_cnt": 1,
    "released": true,
    "server_type": "dify",
    "type": "builtin"
}
```

## 🎯 前端展示效果

1. **工具列表页面**: 显示所有类型的工具，包括新的Dify插件
2. **分类筛选**: 点击"Dify"按钮时，只显示server_type为"dify"的工具
3. **插件信息**: 显示插件名称、描述、版本等信息

## 🔄 API调用流程

```
前端请求 → GET /api/v1/app/collections
    ↓
Backend → ListToolCollectionBaseInfos()
    ↓
调用 → GetDifyPlugins()
    ↓
HTTP请求 → http://localhost:8080/v1/dify-plugins/installed
    ↓
数据转换 → APIToolCollectionBaseDO格式
    ↓
合并返回 → 包含所有类型的工具集合
```

## ✅ 验证要点

1. **API响应**: 确保agent-tool-api的dify-plugins接口正常工作
2. **数据转换**: 验证Dify插件数据正确转换为工具集合格式
3. **前端显示**: 确认前端能正确显示和筛选Dify插件
4. **错误处理**: 当agent-tool-api不可用时，不影响其他工具的显示

## 🚀 使用方式

1. 启动agent-tool-api服务
2. 确保Dify插件API正常工作
3. 前端访问应用工具页面
4. 点击不同分类按钮查看对应工具
