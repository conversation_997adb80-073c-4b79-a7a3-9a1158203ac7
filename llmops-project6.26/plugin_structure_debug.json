{"plugins": [{"id": "e6e54dbe-09c3-4f09-8f7b-53d4733cdc11", "created_at": "2025-07-25T03:17:56.657811Z", "updated_at": "2025-07-25T03:17:56.657811Z", "tenant_id": "f24e8fa4-b522-48b2-a5ff-fe639b38d089", "endpoints_setups": 0, "endpoints_active": 0, "runtime_type": "local", "source": "marketplace", "meta": {"plugin_unique_identifier": "langgenius/siliconflow:0.0.20@a0297ff9ba92d57b12efa51dad87bbf68f6556979d2f32ed15fc833a3a1f4f39"}, "plugin_id": "langgenius/siliconflow", "plugin_unique_identifier": "langgenius/siliconflow:0.0.20@a0297ff9ba92d57b12efa51dad87bbf68f6556979d2f32ed15fc833a3a1f4f39", "version": "0.0.20", "checksum": "a0297ff9ba92d57b12efa51dad87bbf68f6556979d2f32ed15fc833a3a1f4f39", "declaration": {"version": "0.0.20", "author": "lang<PERSON><PERSON>", "name": "siliconflow", "description": {"en_US": "SiliconFlow provides access to various models (LLMs, text embedding, reranking, STT, TTS), configurable via model name, API key, and other parameters.", "zh_Hans": "硅基流动提供对各种模型（LLM、文本嵌入、重排序、STT、TTS）的访问，可通过模型名称、API密钥和其他参数进行配置。", "pt_BR": "SiliconFlow provides access to various models (LLMs, text embedding, reranking, STT, TTS), configurable via model name, API key, and other parameters.", "ja_JP": "SiliconFlow provides access to various models (LLMs, text embedding, reranking, STT, TTS), configurable via model name, API key, and other parameters."}, "icon": "0179a4422f60cc28de38c7b3395b8504c9a7516c050d355849784a46bcea3b91.svg", "label": {"en_US": "SiliconFlow", "zh_Hans": "硅基流动", "pt_BR": "SiliconFlow", "ja_JP": "SiliconFlow"}, "category": "model", "created_at": "2024-09-20T00:13:50.292989-04:00", "resource": {"memory": 268435456, "permission": {"tool": null, "model": {"enabled": false, "llm": false, "text_embedding": false, "rerank": false, "tts": false, "speech2text": false, "moderation": false}, "node": null, "endpoint": null, "storage": null}}, "plugins": {"tools": null, "models": ["provider/siliconflow.yaml"], "endpoints": null}, "tags": [], "repo": null, "verified": true, "tool": null, "model": {"provider": "siliconflow", "label": {"zh_Hans": "硅基流动", "en_US": "SiliconFlow"}, "description": {"zh_Hans": "硅基流动提供对各种模型（LLM、文本嵌入、重排序、STT、TTS）的访问，可通过模型名称、API密钥和其他参数进行配置。", "en_US": "SiliconFlow provides access to various models (LLMs, text embedding, reranking, STT, TTS), configurable via model name, API key, and other parameters."}, "icon_small": {"zh_Hans": "0179a4422f60cc28de38c7b3395b8504c9a7516c050d355849784a46bcea3b91.svg", "en_US": "0179a4422f60cc28de38c7b3395b8504c9a7516c050d355849784a46bcea3b91.svg"}, "icon_large": {"zh_Hans": "8460123b0019ed061306a8d48a5cad5e44f64af4c0d42691b0702433879a963a.svg", "en_US": "8460123b0019ed061306a8d48a5cad5e44f64af4c0d42691b0702433879a963a.svg"}, "background": "#ffecff", "help": {"title": {"zh_Hans": "从 SiliconFlow 获取 API Key", "en_US": "Get your API Key from SiliconFlow"}, "url": {"zh_Hans": "https://cloud.siliconflow.cn/account/ak", "en_US": "https://cloud.siliconflow.cn/account/ak"}}, "supported_model_types": ["llm", "text-embedding", "rerank", "speech2text", "tts"], "configurate_methods": ["predefined-model", "customizable-model"], "models": [{"model": "OpenGVLab/InternVL2-26B", "label": {"zh_Hans": "OpenGVLab/InternVL2-26B", "en_US": "OpenGVLab/InternVL2-26B"}, "model_type": "llm", "features": ["vision"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "用于控制随机性和多样性的程度。具体来说，temperature值控制了生成文本时对每个候选词的概率分布进行平滑的程度。较高的temperature值会降低概率分布的峰值，使得更多的低概率词被选择，生成结果更加多样化；而较低的temperature值则会增强概率分布的峰值，使得高概率词更容易被选择，生成结果更加确定。", "en_US": "Used to control the degree of randomness and diversity. Specifically, the temperature value controls the degree to which the probability distribution of each candidate word is smoothed when generating text. A higher temperature value will reduce the peak value of the probability distribution, allowing more low-probability words to be selected, and the generated results will be more diverse; while a lower temperature value will enhance the peak value of the probability distribution, making it easier for high-probability words to be selected. , the generated results are more certain."}, "required": false, "default": 0.3, "min": 0.0, "max": 2.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "用于指定模型在生成内容时token的最大数量，它定义了生成的上限，但不保证每次都会生成到这个数量。", "en_US": "It is used to specify the maximum number of tokens when the model generates content. It defines the upper limit of generation, but does not guarantee that this number will be generated every time."}, "required": false, "default": 2000, "min": 1.0, "max": 2000.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值范围为（0,1.0)，取值越大，生成的随机性越高；取值越低，生成的确定性越高。", "en_US": "The probability threshold of the kernel sampling method during the generation process. For example, when the value is 0.8, only the smallest set of the most likely tokens with a sum of probabilities greater than or equal to 0.8 is retained as the candidate set. The value range is (0,1.0). The larger the value, the higher the randomness generated; the lower the value, the higher the certainty generated."}, "required": false, "default": 0.8, "min": 0.1, "max": 0.9, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "生成时，采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高。", "en_US": "The size of the sample candidate set when generated. For example, when the value is 50, only the 50 highest-scoring tokens in a single generation form a randomly sampled candidate set. The larger the value, the higher the randomness generated; the smaller the value, the higher the certainty generated."}, "required": false, "default": null, "min": 0.0, "max": 99.0, "precision": null, "options": []}, {"name": "seed", "use_template": null, "label": {"zh_Hans": "随机种子", "en_US": "Random seed"}, "type": "int", "help": {"zh_Hans": "生成时使用的随机数种子，用户控制模型生成内容的随机性。支持无符号64位整数，默认值为 1234。在使用seed时，模型将尽可能生成相同或相似的结果，但目前不保证每次生成的结果完全相同。", "en_US": "The random number seed used when generating, the user controls the randomness of the content generated by the model. Supports unsigned 64-bit integers, default value is 1234. When using seed, the model will try its best to generate the same or similar results, but there is currently no guarantee that the results will be exactly the same every time."}, "required": false, "default": 1234, "min": null, "max": null, "precision": null, "options": []}, {"name": "repetition_penalty", "use_template": null, "label": {"zh_Hans": "重复惩罚", "en_US": "Repetition penalty"}, "type": "float", "help": {"zh_Hans": "用于控制模型生成时的重复度。提高repetition_penalty时可以降低模型生成的重复度。1.0表示不做惩罚。", "en_US": "Used to control the repeatability when generating models. Increasing repetition_penalty can reduce the duplication of model generation. 1.0 means no punishment."}, "required": false, "default": 1.1, "min": null, "max": null, "precision": null, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "21", "output": "21", "unit": "0.000001", "currency": "RMB"}}, {"model": "Pro/OpenGVLab/InternVL2-8B", "label": {"zh_Hans": "Pro/OpenGVLab/InternVL2-8B", "en_US": "Pro/OpenGVLab/InternVL2-8B"}, "model_type": "llm", "features": ["vision"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "用于控制随机性和多样性的程度。具体来说，temperature值控制了生成文本时对每个候选词的概率分布进行平滑的程度。较高的temperature值会降低概率分布的峰值，使得更多的低概率词被选择，生成结果更加多样化；而较低的temperature值则会增强概率分布的峰值，使得高概率词更容易被选择，生成结果更加确定。", "en_US": "Used to control the degree of randomness and diversity. Specifically, the temperature value controls the degree to which the probability distribution of each candidate word is smoothed when generating text. A higher temperature value will reduce the peak value of the probability distribution, allowing more low-probability words to be selected, and the generated results will be more diverse; while a lower temperature value will enhance the peak value of the probability distribution, making it easier for high-probability words to be selected. , the generated results are more certain."}, "required": false, "default": 0.3, "min": 0.0, "max": 2.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "用于指定模型在生成内容时token的最大数量，它定义了生成的上限，但不保证每次都会生成到这个数量。", "en_US": "It is used to specify the maximum number of tokens when the model generates content. It defines the upper limit of generation, but does not guarantee that this number will be generated every time."}, "required": false, "default": 2000, "min": 1.0, "max": 2000.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值范围为（0,1.0)，取值越大，生成的随机性越高；取值越低，生成的确定性越高。", "en_US": "The probability threshold of the kernel sampling method during the generation process. For example, when the value is 0.8, only the smallest set of the most likely tokens with a sum of probabilities greater than or equal to 0.8 is retained as the candidate set. The value range is (0,1.0). The larger the value, the higher the randomness generated; the lower the value, the higher the certainty generated."}, "required": false, "default": 0.8, "min": 0.1, "max": 0.9, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "生成时，采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高。", "en_US": "The size of the sample candidate set when generated. For example, when the value is 50, only the 50 highest-scoring tokens in a single generation form a randomly sampled candidate set. The larger the value, the higher the randomness generated; the smaller the value, the higher the certainty generated."}, "required": false, "default": null, "min": 0.0, "max": 99.0, "precision": null, "options": []}, {"name": "seed", "use_template": null, "label": {"zh_Hans": "随机种子", "en_US": "Random seed"}, "type": "int", "help": {"zh_Hans": "生成时使用的随机数种子，用户控制模型生成内容的随机性。支持无符号64位整数，默认值为 1234。在使用seed时，模型将尽可能生成相同或相似的结果，但目前不保证每次生成的结果完全相同。", "en_US": "The random number seed used when generating, the user controls the randomness of the content generated by the model. Supports unsigned 64-bit integers, default value is 1234. When using seed, the model will try its best to generate the same or similar results, but there is currently no guarantee that the results will be exactly the same every time."}, "required": false, "default": 1234, "min": null, "max": null, "precision": null, "options": []}, {"name": "repetition_penalty", "use_template": null, "label": {"zh_Hans": "重复惩罚", "en_US": "Repetition penalty"}, "type": "float", "help": {"zh_Hans": "用于控制模型生成时的重复度。提高repetition_penalty时可以降低模型生成的重复度。1.0表示不做惩罚。", "en_US": "Used to control the repeatability when generating models. Increasing repetition_penalty can reduce the duplication of model generation. 1.0 means no punishment."}, "required": false, "default": 1.1, "min": null, "max": null, "precision": null, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "21", "output": "21", "unit": "0.000001", "currency": "RMB"}}, {"model": "deepseek-ai/DeepSeek-Coder-V2-Instruct", "label": {"zh_Hans": "deepseek-ai/DeepSeek-Coder-V2-Instruct", "en_US": "deepseek-ai/DeepSeek-Coder-V2-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}], "pricing": {"input": "1.33", "output": "1.33", "unit": "0.000001", "currency": "RMB"}}, {"model": "deepseek-ai/DeepSeek-R1-Distill-Llama-70B", "label": {"zh_Hans": "deepseek-ai/DeepSeek-R1-Distill-Llama-70B", "en_US": "deepseek-ai/DeepSeek-R1-Distill-Llama-70B"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32000, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0.6, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 0.95, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 8192, "min": 1.0, "max": 16384.0, "precision": 0, "options": []}], "pricing": {"input": "4.13", "output": "4.13", "unit": "0.000001", "currency": "RMB"}}, {"model": "deepseek-ai/DeepSeek-R1-Distill-Llama-8B", "label": {"zh_Hans": "deepseek-ai/DeepSeek-R1-Distill-Llama-8B", "en_US": "deepseek-ai/DeepSeek-R1-Distill-Llama-8B"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32000, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0.6, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 0.95, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 8192, "min": 1.0, "max": 16384.0, "precision": 0, "options": []}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B", "label": {"zh_Hans": "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B", "en_US": "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32000, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0.6, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 0.95, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 8192, "min": 1.0, "max": 16384.0, "precision": 0, "options": []}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B", "label": {"zh_Hans": "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B", "en_US": "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32000, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0.6, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 0.95, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 8192, "min": 1.0, "max": 16384.0, "precision": 0, "options": []}], "pricing": {"input": "0.7", "output": "0.7", "unit": "0.000001", "currency": "RMB"}}, {"model": "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B", "label": {"zh_Hans": "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B", "en_US": "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32000, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0.6, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 0.95, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 8192, "min": 1.0, "max": 16384.0, "precision": 0, "options": []}], "pricing": {"input": "1.26", "output": "1.26", "unit": "0.000001", "currency": "RMB"}}, {"model": "deepseek-ai/DeepSeek-R1-<PERSON>still-Qwen-7B", "label": {"zh_Hans": "deepseek-ai/DeepSeek-R1-<PERSON>still-Qwen-7B", "en_US": "deepseek-ai/DeepSeek-R1-<PERSON>still-Qwen-7B"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32000, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0.6, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 0.95, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 8192, "min": 1.0, "max": 16384.0, "precision": 0, "options": []}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "Pro/deepseek-ai/DeepSeek-R1", "label": {"zh_Hans": "Pro/deepseek-ai/DeepSeek-R1", "en_US": "Pro/deepseek-ai/DeepSeek-R1"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 64000, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0.6, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 0.95, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 16384, "min": 1.0, "max": 16384.0, "precision": 0, "options": []}], "pricing": {"input": "4", "output": "16", "unit": "0.000001", "currency": "RMB"}}, {"model": "deepseek-ai/DeepSeek-R1", "label": {"zh_Hans": "deepseek-ai/DeepSeek-R1", "en_US": "deepseek-ai/DeepSeek-R1"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 64000, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0.6, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 0.95, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 16384, "min": 1.0, "max": 16384.0, "precision": 0, "options": []}], "pricing": {"input": "4", "output": "16", "unit": "0.000001", "currency": "RMB"}}, {"model": "deepseek-ai/DeepSeek-V2-Chat", "label": {"zh_Hans": "deepseek-ai/DeepSeek-V2-Chat", "en_US": "deepseek-ai/DeepSeek-V2-Chat"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}], "pricing": {"input": "1.33", "output": "1.33", "unit": "0.000001", "currency": "RMB"}}, {"model": "deepseek-ai/DeepSeek-V2.5", "label": {"zh_Hans": "deepseek-ai/DeepSeek-V2.5", "en_US": "deepseek-ai/DeepSeek-V2.5"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "1.33", "output": "1.33", "unit": "0.000001", "currency": "RMB"}}, {"model": "Pro/deepseek-ai/DeepSeek-V3", "label": {"zh_Hans": "Pro/deepseek-ai/DeepSeek-V3", "en_US": "Pro/deepseek-ai/DeepSeek-V3"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 64000, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 4096, "min": 1.0, "max": 8192.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "2", "output": "8", "unit": "0.000001", "currency": "RMB"}}, {"model": "deepseek-ai/DeepSeek-V3", "label": {"zh_Hans": "deepseek-ai/DeepSeek-V3", "en_US": "deepseek-ai/DeepSeek-V3"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 64000, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 4096, "min": 1.0, "max": 8192.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "2", "output": "8", "unit": "0.000001", "currency": "RMB"}}, {"model": "google/gemma-2-27b-it", "label": {"zh_Hans": "google/gemma-2-27b-it", "en_US": "google/gemma-2-27b-it"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 8196, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "1.26", "output": "1.26", "unit": "0.000001", "currency": "RMB"}}, {"model": "google/gemma-2-9b-it", "label": {"zh_Hans": "google/gemma-2-9b-it", "en_US": "google/gemma-2-9b-it"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 8196, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "THUDM/GLM-4.1V-9B-Thinking", "label": {"zh_Hans": "THUDM/glm-41v-9b-thinking", "en_US": "THUDM/glm-41v-9b-thinking"}, "model_type": "llm", "features": ["vision"], "fetch_from": "predefined-model", "model_properties": {"context_size": 16384, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0.95, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 8192, "min": 1.0, "max": 16384.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 0.7, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": 2, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 1.1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text"]}], "pricing": {"input": "0", "output": "0", "unit": "0", "currency": "RMB"}}, {"model": "THUDM/glm-4-9b-chat", "label": {"zh_Hans": "THUDM/glm-4-9b-chat", "en_US": "THUDM/glm-4-9b-chat"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "Tencent/Hunyuan-A52B-Instruct", "label": {"zh_Hans": "Tencent/Hunyuan-A52B-Instruct", "en_US": "Tencent/Hunyuan-A52B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "用于控制随机性和多样性的程度。具体来说，temperature值控制了生成文本时对每个候选词的概率分布进行平滑的程度。较高的temperature值会降低概率分布的峰值，使得更多的低概率词被选择，生成结果更加多样化；而较低的temperature值则会增强概率分布的峰值，使得高概率词更容易被选择，生成结果更加确定。", "en_US": "Used to control the degree of randomness and diversity. Specifically, the temperature value controls the degree to which the probability distribution of each candidate word is smoothed when generating text. A higher temperature value will reduce the peak value of the probability distribution, allowing more low-probability words to be selected, and the generated results will be more diverse; while a lower temperature value will enhance the peak value of the probability distribution, making it easier for high-probability words to be selected. , the generated results are more certain."}, "required": false, "default": 0.3, "min": 0.0, "max": 2.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "用于指定模型在生成内容时token的最大数量，它定义了生成的上限，但不保证每次都会生成到这个数量。", "en_US": "It is used to specify the maximum number of tokens when the model generates content. It defines the upper limit of generation, but does not guarantee that this number will be generated every time."}, "required": false, "default": 2000, "min": 1.0, "max": 2000.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值范围为（0,1.0)，取值越大，生成的随机性越高；取值越低，生成的确定性越高。", "en_US": "The probability threshold of the kernel sampling method during the generation process. For example, when the value is 0.8, only the smallest set of the most likely tokens with a sum of probabilities greater than or equal to 0.8 is retained as the candidate set. The value range is (0,1.0). The larger the value, the higher the randomness generated; the lower the value, the higher the certainty generated."}, "required": false, "default": 0.8, "min": 0.1, "max": 0.9, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "生成时，采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高。", "en_US": "The size of the sample candidate set when generated. For example, when the value is 50, only the 50 highest-scoring tokens in a single generation form a randomly sampled candidate set. The larger the value, the higher the randomness generated; the smaller the value, the higher the certainty generated."}, "required": false, "default": null, "min": 0.0, "max": 99.0, "precision": null, "options": []}, {"name": "seed", "use_template": null, "label": {"zh_Hans": "随机种子", "en_US": "Random seed"}, "type": "int", "help": {"zh_Hans": "生成时使用的随机数种子，用户控制模型生成内容的随机性。支持无符号64位整数，默认值为 1234。在使用seed时，模型将尽可能生成相同或相似的结果，但目前不保证每次生成的结果完全相同。", "en_US": "The random number seed used when generating, the user controls the randomness of the content generated by the model. Supports unsigned 64-bit integers, default value is 1234. When using seed, the model will try its best to generate the same or similar results, but there is currently no guarantee that the results will be exactly the same every time."}, "required": false, "default": 1234, "min": null, "max": null, "precision": null, "options": []}, {"name": "repetition_penalty", "use_template": null, "label": {"zh_Hans": "重复惩罚", "en_US": "Repetition penalty"}, "type": "float", "help": {"zh_Hans": "用于控制模型生成时的重复度。提高repetition_penalty时可以降低模型生成的重复度。1.0表示不做惩罚。", "en_US": "Used to control the repeatability when generating models. Increasing repetition_penalty can reduce the duplication of model generation. 1.0 means no punishment."}, "required": false, "default": 1.1, "min": null, "max": null, "precision": null, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "21", "output": "21", "unit": "0.000001", "currency": "RMB"}}, {"model": "internlm/internlm2_5-20b-chat", "label": {"zh_Hans": "internlm/internlm2_5-20b-chat", "en_US": "internlm/internlm2_5-20b-chat"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "1", "output": "1", "unit": "0.000001", "currency": "RMB"}}, {"model": "internlm/internlm2_5-7b-chat", "label": {"zh_Hans": "internlm/internlm2_5-7b-chat", "en_US": "internlm/internlm2_5-7b-chat"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "OpenGVLab/InternVL2-Llama3-76B", "label": {"zh_Hans": "OpenGVLab/InternVL2-Llama3-76B", "en_US": "OpenGVLab/InternVL2-Llama3-76B"}, "model_type": "llm", "features": ["vision"], "fetch_from": "predefined-model", "model_properties": {"context_size": 8192, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "用于控制随机性和多样性的程度。具体来说，temperature值控制了生成文本时对每个候选词的概率分布进行平滑的程度。较高的temperature值会降低概率分布的峰值，使得更多的低概率词被选择，生成结果更加多样化；而较低的temperature值则会增强概率分布的峰值，使得高概率词更容易被选择，生成结果更加确定。", "en_US": "Used to control the degree of randomness and diversity. Specifically, the temperature value controls the degree to which the probability distribution of each candidate word is smoothed when generating text. A higher temperature value will reduce the peak value of the probability distribution, allowing more low-probability words to be selected, and the generated results will be more diverse; while a lower temperature value will enhance the peak value of the probability distribution, making it easier for high-probability words to be selected. , the generated results are more certain."}, "required": false, "default": 0.3, "min": 0.0, "max": 2.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "用于指定模型在生成内容时token的最大数量，它定义了生成的上限，但不保证每次都会生成到这个数量。", "en_US": "It is used to specify the maximum number of tokens when the model generates content. It defines the upper limit of generation, but does not guarantee that this number will be generated every time."}, "required": false, "default": 2000, "min": 1.0, "max": 2000.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值范围为（0,1.0)，取值越大，生成的随机性越高；取值越低，生成的确定性越高。", "en_US": "The probability threshold of the kernel sampling method during the generation process. For example, when the value is 0.8, only the smallest set of the most likely tokens with a sum of probabilities greater than or equal to 0.8 is retained as the candidate set. The value range is (0,1.0). The larger the value, the higher the randomness generated; the lower the value, the higher the certainty generated."}, "required": false, "default": 0.8, "min": 0.1, "max": 0.9, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "生成时，采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高。", "en_US": "The size of the sample candidate set when generated. For example, when the value is 50, only the 50 highest-scoring tokens in a single generation form a randomly sampled candidate set. The larger the value, the higher the randomness generated; the smaller the value, the higher the certainty generated."}, "required": false, "default": null, "min": 0.0, "max": 99.0, "precision": null, "options": []}, {"name": "seed", "use_template": null, "label": {"zh_Hans": "随机种子", "en_US": "Random seed"}, "type": "int", "help": {"zh_Hans": "生成时使用的随机数种子，用户控制模型生成内容的随机性。支持无符号64位整数，默认值为 1234。在使用seed时，模型将尽可能生成相同或相似的结果，但目前不保证每次生成的结果完全相同。", "en_US": "The random number seed used when generating, the user controls the randomness of the content generated by the model. Supports unsigned 64-bit integers, default value is 1234. When using seed, the model will try its best to generate the same or similar results, but there is currently no guarantee that the results will be exactly the same every time."}, "required": false, "default": 1234, "min": null, "max": null, "precision": null, "options": []}, {"name": "repetition_penalty", "use_template": null, "label": {"zh_Hans": "重复惩罚", "en_US": "Repetition penalty"}, "type": "float", "help": {"zh_Hans": "用于控制模型生成时的重复度。提高repetition_penalty时可以降低模型生成的重复度。1.0表示不做惩罚。", "en_US": "Used to control the repeatability when generating models. Increasing repetition_penalty can reduce the duplication of model generation. 1.0 means no punishment."}, "required": false, "default": 1.1, "min": null, "max": null, "precision": null, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "4.13", "output": "4.13", "unit": "0.000001", "currency": "RMB"}}, {"model": "deepseek-ai/Janus-Pro-7B", "label": {"zh_Hans": "deepseek-ai/Janus-Pro-7B", "en_US": "deepseek-ai/Janus-Pro-7B"}, "model_type": "llm", "features": ["vision"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32000, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 4096, "min": 1.0, "max": 8192.0, "precision": 0, "options": []}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "Pro/moonshotai/Kimi-K2-Instruct", "label": {"zh_Hans": "Pro/moonshotai/Kimi-K2-Instruct", "en_US": "Pro/moonshotai/Kimi-K2-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 128000, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0.6, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 16384, "min": 1.0, "max": 16384.0, "precision": 0, "options": []}], "pricing": {"input": "4", "output": "16", "unit": "0.000001", "currency": "RMB"}}, {"model": "moonshotai/Kimi-K2-Instruct", "label": {"zh_Hans": "moonshotai/Kimi-K2-Instruct", "en_US": "moonshotai/Kimi-K2-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 128000, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0.6, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 16384, "min": 1.0, "max": 16384.0, "precision": 0, "options": []}], "pricing": {"input": "4", "output": "16", "unit": "0.000001", "currency": "RMB"}}, {"model": "meta-llama/Llama-3.3-70B-Instruct", "label": {"zh_Hans": "meta-llama/Llama-3.3-70B-Instruct", "en_US": "meta-llama/Llama-3.3-70B-Instruct"}, "model_type": "llm", "features": ["agent-thought", "tool-call", "stream-tool-call"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "4.13", "output": "4.13", "unit": "0.000001", "currency": "RMB"}}, {"model": "meta-llama/Meta-Llama-3-70B-Instruct", "label": {"zh_Hans": "meta-llama/Meta-Llama-3-70B-Instruct", "en_US": "meta-llama/Meta-Llama-3-70B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}], "pricing": {"input": "4.13", "output": "4.13", "unit": "0.000001", "currency": "RMB"}}, {"model": "meta-llama/Meta-Llama-3-8B-Instruct", "label": {"zh_Hans": "meta-llama/Meta-Llama-3-8B-Instruct", "en_US": "meta-llama/Meta-Llama-3-8B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 8192, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "meta-llama/Meta-Llama-3.1-405B-Instruct", "label": {"zh_Hans": "meta-llama/Meta-Llama-3.1-405B-Instruct", "en_US": "meta-llama/Meta-Llama-3.1-405B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "21", "output": "21", "unit": "0.000001", "currency": "RMB"}}, {"model": "meta-llama/Meta-Llama-3.1-70B-Instruct", "label": {"zh_Hans": "meta-llama/Meta-Llama-3.1-70B-Instruct", "en_US": "meta-llama/Meta-Llama-3.1-70B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 8192, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "4.13", "output": "4.13", "unit": "0.000001", "currency": "RMB"}}, {"model": "meta-llama/Meta-Llama-3.1-8B-Instruct", "label": {"zh_Hans": "meta-llama/Meta-Llama-3.1-8B-Instruct", "en_US": "meta-llama/Meta-Llama-3.1-8B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 8192, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "mistralai/Mistral-7B-Instruct-v0.2", "label": {"zh_Hans": "mistralai/Mistral-7B-Instruct-v0.2", "en_US": "mistralai/Mistral-7B-Instruct-v0.2"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "mistralai/Mixtral-8x7B-Instruct-v0.1", "label": {"zh_Hans": "mistralai/Mixtral-8x7B-Instruct-v0.1", "en_US": "mistralai/Mixtral-8x7B-Instruct-v0.1"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}], "pricing": {"input": "1.26", "output": "1.26", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/QVQ-72B-Preview", "label": {"zh_Hans": "Qwen/QVQ-72B-Preview", "en_US": "Qwen/QVQ-72B-Preview"}, "model_type": "llm", "features": ["agent-thought", "tool-call", "stream-tool-call", "vision"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 8192, "min": 1.0, "max": 16384.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "9.9", "output": "9.9", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/QwQ-32B-Preview", "label": {"zh_Hans": "Qwen/QwQ-32B-Preview", "en_US": "Qwen/QwQ-32B-Preview"}, "model_type": "llm", "features": ["agent-thought", "tool-call", "stream-tool-call"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 4096, "min": 1.0, "max": 8192.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "1.26", "output": "1.26", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/QwQ-32B", "label": {"zh_Hans": "Qwen/QwQ-32B", "en_US": "Qwen/QwQ-32B"}, "model_type": "llm", "features": ["agent-thought", "tool-call", "stream-tool-call"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 8192, "min": 1.0, "max": 32768.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "1", "output": "4", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen2-1.5B-Instruct", "label": {"zh_Hans": "Qwen/Qwen2-1.5B-Instruct", "en_US": "Qwen/Qwen2-1.5B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen2-57B-A14B-Instruct", "label": {"zh_Hans": "Qwen/Qwen2-57B-A14B-Instruct", "en_US": "Qwen/Qwen2-57B-A14B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}], "pricing": {"input": "1.26", "output": "1.26", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen2-72B-Instruct", "label": {"zh_Hans": "Qwen/Qwen2-72B-Instruct", "en_US": "Qwen/Qwen2-72B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}], "pricing": {"input": "4.13", "output": "4.13", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen2-7B-Instruct", "label": {"zh_Hans": "Qwen/Qwen2-7B-Instruct", "en_US": "Qwen/Qwen2-7B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen2-VL-72B-Instruct", "label": {"zh_Hans": "Qwen/Qwen2-VL-72B-Instruct", "en_US": "Qwen/Qwen2-VL-72B-Instruct"}, "model_type": "llm", "features": ["vision"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "用于控制随机性和多样性的程度。具体来说，temperature值控制了生成文本时对每个候选词的概率分布进行平滑的程度。较高的temperature值会降低概率分布的峰值，使得更多的低概率词被选择，生成结果更加多样化；而较低的temperature值则会增强概率分布的峰值，使得高概率词更容易被选择，生成结果更加确定。", "en_US": "Used to control the degree of randomness and diversity. Specifically, the temperature value controls the degree to which the probability distribution of each candidate word is smoothed when generating text. A higher temperature value will reduce the peak value of the probability distribution, allowing more low-probability words to be selected, and the generated results will be more diverse; while a lower temperature value will enhance the peak value of the probability distribution, making it easier for high-probability words to be selected. , the generated results are more certain."}, "required": false, "default": 0.3, "min": 0.0, "max": 2.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "用于指定模型在生成内容时token的最大数量，它定义了生成的上限，但不保证每次都会生成到这个数量。", "en_US": "It is used to specify the maximum number of tokens when the model generates content. It defines the upper limit of generation, but does not guarantee that this number will be generated every time."}, "required": false, "default": 2000, "min": 1.0, "max": 2000.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值范围为（0,1.0)，取值越大，生成的随机性越高；取值越低，生成的确定性越高。", "en_US": "The probability threshold of the kernel sampling method during the generation process. For example, when the value is 0.8, only the smallest set of the most likely tokens with a sum of probabilities greater than or equal to 0.8 is retained as the candidate set. The value range is (0,1.0). The larger the value, the higher the randomness generated; the lower the value, the higher the certainty generated."}, "required": false, "default": 0.8, "min": 0.1, "max": 0.9, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "生成时，采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高。", "en_US": "The size of the sample candidate set when generated. For example, when the value is 50, only the 50 highest-scoring tokens in a single generation form a randomly sampled candidate set. The larger the value, the higher the randomness generated; the smaller the value, the higher the certainty generated."}, "required": false, "default": null, "min": 0.0, "max": 99.0, "precision": null, "options": []}, {"name": "seed", "use_template": null, "label": {"zh_Hans": "随机种子", "en_US": "Random seed"}, "type": "int", "help": {"zh_Hans": "生成时使用的随机数种子，用户控制模型生成内容的随机性。支持无符号64位整数，默认值为 1234。在使用seed时，模型将尽可能生成相同或相似的结果，但目前不保证每次生成的结果完全相同。", "en_US": "The random number seed used when generating, the user controls the randomness of the content generated by the model. Supports unsigned 64-bit integers, default value is 1234. When using seed, the model will try its best to generate the same or similar results, but there is currently no guarantee that the results will be exactly the same every time."}, "required": false, "default": 1234, "min": null, "max": null, "precision": null, "options": []}, {"name": "repetition_penalty", "use_template": null, "label": {"zh_Hans": "重复惩罚", "en_US": "Repetition penalty"}, "type": "float", "help": {"zh_Hans": "用于控制模型生成时的重复度。提高repetition_penalty时可以降低模型生成的重复度。1.0表示不做惩罚。", "en_US": "Used to control the repeatability when generating models. Increasing repetition_penalty can reduce the duplication of model generation. 1.0 means no punishment."}, "required": false, "default": 1.1, "min": null, "max": null, "precision": null, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "4.13", "output": "4.13", "unit": "0.000001", "currency": "RMB"}}, {"model": "Pro/Qwen/Qwen2-VL-7B-Instruct", "label": {"zh_Hans": "Pro/Qwen/Qwen2-VL-7B-Instruct", "en_US": "Pro/Qwen/Qwen2-VL-7B-Instruct"}, "model_type": "llm", "features": ["vision"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "用于控制随机性和多样性的程度。具体来说，temperature值控制了生成文本时对每个候选词的概率分布进行平滑的程度。较高的temperature值会降低概率分布的峰值，使得更多的低概率词被选择，生成结果更加多样化；而较低的temperature值则会增强概率分布的峰值，使得高概率词更容易被选择，生成结果更加确定。", "en_US": "Used to control the degree of randomness and diversity. Specifically, the temperature value controls the degree to which the probability distribution of each candidate word is smoothed when generating text. A higher temperature value will reduce the peak value of the probability distribution, allowing more low-probability words to be selected, and the generated results will be more diverse; while a lower temperature value will enhance the peak value of the probability distribution, making it easier for high-probability words to be selected. , the generated results are more certain."}, "required": false, "default": 0.3, "min": 0.0, "max": 2.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "用于指定模型在生成内容时token的最大数量，它定义了生成的上限，但不保证每次都会生成到这个数量。", "en_US": "It is used to specify the maximum number of tokens when the model generates content. It defines the upper limit of generation, but does not guarantee that this number will be generated every time."}, "required": false, "default": 2000, "min": 1.0, "max": 2000.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值范围为（0,1.0)，取值越大，生成的随机性越高；取值越低，生成的确定性越高。", "en_US": "The probability threshold of the kernel sampling method during the generation process. For example, when the value is 0.8, only the smallest set of the most likely tokens with a sum of probabilities greater than or equal to 0.8 is retained as the candidate set. The value range is (0,1.0). The larger the value, the higher the randomness generated; the lower the value, the higher the certainty generated."}, "required": false, "default": 0.8, "min": 0.1, "max": 0.9, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "生成时，采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高。", "en_US": "The size of the sample candidate set when generated. For example, when the value is 50, only the 50 highest-scoring tokens in a single generation form a randomly sampled candidate set. The larger the value, the higher the randomness generated; the smaller the value, the higher the certainty generated."}, "required": false, "default": null, "min": 0.0, "max": 99.0, "precision": null, "options": []}, {"name": "seed", "use_template": null, "label": {"zh_Hans": "随机种子", "en_US": "Random seed"}, "type": "int", "help": {"zh_Hans": "生成时使用的随机数种子，用户控制模型生成内容的随机性。支持无符号64位整数，默认值为 1234。在使用seed时，模型将尽可能生成相同或相似的结果，但目前不保证每次生成的结果完全相同。", "en_US": "The random number seed used when generating, the user controls the randomness of the content generated by the model. Supports unsigned 64-bit integers, default value is 1234. When using seed, the model will try its best to generate the same or similar results, but there is currently no guarantee that the results will be exactly the same every time."}, "required": false, "default": 1234, "min": null, "max": null, "precision": null, "options": []}, {"name": "repetition_penalty", "use_template": null, "label": {"zh_Hans": "重复惩罚", "en_US": "Repetition penalty"}, "type": "float", "help": {"zh_Hans": "用于控制模型生成时的重复度。提高repetition_penalty时可以降低模型生成的重复度。1.0表示不做惩罚。", "en_US": "Used to control the repeatability when generating models. Increasing repetition_penalty can reduce the duplication of model generation. 1.0 means no punishment."}, "required": false, "default": 1.1, "min": null, "max": null, "precision": null, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "0.35", "output": "0.35", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen2.5-14B-Instruct", "label": {"zh_Hans": "Qwen/Qwen2.5-14B-Instruct", "en_US": "Qwen/Qwen2.5-14B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 8192.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "0.7", "output": "0.7", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen2.5-32B-Instruct", "label": {"zh_Hans": "Qwen/Qwen2.5-32B-Instruct", "en_US": "Qwen/Qwen2.5-32B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 8192.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "1.26", "output": "1.26", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen2.5-72B-Instruct-128K", "label": {"zh_Hans": "Qwen/Qwen2.5-72B-Instruct-128K", "en_US": "Qwen/Qwen2.5-72B-Instruct-128K"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 131072, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "4.13", "output": "4.13", "unit": "0.000001", "currency": "RMB"}}, {"model": "Vendor-A/Qwen/Qwen2.5-72B-Instruct", "label": {"zh_Hans": "Vendor-A/Qwen/Qwen2.5-72B-Instruct", "en_US": "Vendor-A/Qwen/Qwen2.5-72B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "1", "output": "1", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen2.5-72B-Instruct", "label": {"zh_Hans": "Qwen/Qwen2.5-72B-Instruct", "en_US": "Qwen/Qwen2.5-72B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "4.13", "output": "4.13", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen2.5-7B-Instruct", "label": {"zh_Hans": "Qwen/Qwen2.5-7B-Instruct", "en_US": "Qwen/Qwen2.5-7B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 8192.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen2.5-Coder-32B-Instruct", "label": {"zh_Hans": "Qwen/Qwen2.5-Coder-32B-Instruct", "en_US": "Qwen/Qwen2.5-Coder-32B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "用于控制随机性和多样性的程度。具体来说，temperature值控制了生成文本时对每个候选词的概率分布进行平滑的程度。较高的temperature值会降低概率分布的峰值，使得更多的低概率词被选择，生成结果更加多样化；而较低的temperature值则会增强概率分布的峰值，使得高概率词更容易被选择，生成结果更加确定。", "en_US": "Used to control the degree of randomness and diversity. Specifically, the temperature value controls the degree to which the probability distribution of each candidate word is smoothed when generating text. A higher temperature value will reduce the peak value of the probability distribution, allowing more low-probability words to be selected, and the generated results will be more diverse; while a lower temperature value will enhance the peak value of the probability distribution, making it easier for high-probability words to be selected. , the generated results are more certain."}, "required": false, "default": 0.3, "min": 0.0, "max": 2.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "用于指定模型在生成内容时token的最大数量，它定义了生成的上限，但不保证每次都会生成到这个数量。", "en_US": "It is used to specify the maximum number of tokens when the model generates content. It defines the upper limit of generation, but does not guarantee that this number will be generated every time."}, "required": false, "default": 8192, "min": 1.0, "max": 8192.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值范围为（0,1.0)，取值越大，生成的随机性越高；取值越低，生成的确定性越高。", "en_US": "The probability threshold of the kernel sampling method during the generation process. For example, when the value is 0.8, only the smallest set of the most likely tokens with a sum of probabilities greater than or equal to 0.8 is retained as the candidate set. The value range is (0,1.0). The larger the value, the higher the randomness generated; the lower the value, the higher the certainty generated."}, "required": false, "default": 0.8, "min": 0.1, "max": 0.9, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "生成时，采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高。", "en_US": "The size of the sample candidate set when generated. For example, when the value is 50, only the 50 highest-scoring tokens in a single generation form a randomly sampled candidate set. The larger the value, the higher the randomness generated; the smaller the value, the higher the certainty generated."}, "required": false, "default": null, "min": 0.0, "max": 99.0, "precision": null, "options": []}, {"name": "seed", "use_template": null, "label": {"zh_Hans": "随机种子", "en_US": "Random seed"}, "type": "int", "help": {"zh_Hans": "生成时使用的随机数种子，用户控制模型生成内容的随机性。支持无符号64位整数，默认值为 1234。在使用seed时，模型将尽可能生成相同或相似的结果，但目前不保证每次生成的结果完全相同。", "en_US": "The random number seed used when generating, the user controls the randomness of the content generated by the model. Supports unsigned 64-bit integers, default value is 1234. When using seed, the model will try its best to generate the same or similar results, but there is currently no guarantee that the results will be exactly the same every time."}, "required": false, "default": 1234, "min": null, "max": null, "precision": null, "options": []}, {"name": "repetition_penalty", "use_template": null, "label": {"zh_Hans": "重复惩罚", "en_US": "Repetition penalty"}, "type": "float", "help": {"zh_Hans": "用于控制模型生成时的重复度。提高repetition_penalty时可以降低模型生成的重复度。1.0表示不做惩罚。", "en_US": "Used to control the repeatability when generating models. Increasing repetition_penalty can reduce the duplication of model generation. 1.0 means no punishment."}, "required": false, "default": 1.1, "min": null, "max": null, "precision": null, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "1.26", "output": "1.26", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen2.5-Coder-7B-Instruct", "label": {"zh_Hans": "Qwen/Qwen2.5-Coder-7B-Instruct", "en_US": "Qwen/Qwen2.5-Coder-7B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 131072, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "用于控制随机性和多样性的程度。具体来说，temperature值控制了生成文本时对每个候选词的概率分布进行平滑的程度。较高的temperature值会降低概率分布的峰值，使得更多的低概率词被选择，生成结果更加多样化；而较低的temperature值则会增强概率分布的峰值，使得高概率词更容易被选择，生成结果更加确定。", "en_US": "Used to control the degree of randomness and diversity. Specifically, the temperature value controls the degree to which the probability distribution of each candidate word is smoothed when generating text. A higher temperature value will reduce the peak value of the probability distribution, allowing more low-probability words to be selected, and the generated results will be more diverse; while a lower temperature value will enhance the peak value of the probability distribution, making it easier for high-probability words to be selected. , the generated results are more certain."}, "required": false, "default": 0.3, "min": 0.0, "max": 2.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "用于指定模型在生成内容时token的最大数量，它定义了生成的上限，但不保证每次都会生成到这个数量。", "en_US": "It is used to specify the maximum number of tokens when the model generates content. It defines the upper limit of generation, but does not guarantee that this number will be generated every time."}, "required": false, "default": 8192, "min": 1.0, "max": 8192.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值范围为（0,1.0)，取值越大，生成的随机性越高；取值越低，生成的确定性越高。", "en_US": "The probability threshold of the kernel sampling method during the generation process. For example, when the value is 0.8, only the smallest set of the most likely tokens with a sum of probabilities greater than or equal to 0.8 is retained as the candidate set. The value range is (0,1.0). The larger the value, the higher the randomness generated; the lower the value, the higher the certainty generated."}, "required": false, "default": 0.8, "min": 0.1, "max": 0.9, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "生成时，采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高。", "en_US": "The size of the sample candidate set when generated. For example, when the value is 50, only the 50 highest-scoring tokens in a single generation form a randomly sampled candidate set. The larger the value, the higher the randomness generated; the smaller the value, the higher the certainty generated."}, "required": false, "default": null, "min": 0.0, "max": 99.0, "precision": null, "options": []}, {"name": "seed", "use_template": null, "label": {"zh_Hans": "随机种子", "en_US": "Random seed"}, "type": "int", "help": {"zh_Hans": "生成时使用的随机数种子，用户控制模型生成内容的随机性。支持无符号64位整数，默认值为 1234。在使用seed时，模型将尽可能生成相同或相似的结果，但目前不保证每次生成的结果完全相同。", "en_US": "The random number seed used when generating, the user controls the randomness of the content generated by the model. Supports unsigned 64-bit integers, default value is 1234. When using seed, the model will try its best to generate the same or similar results, but there is currently no guarantee that the results will be exactly the same every time."}, "required": false, "default": 1234, "min": null, "max": null, "precision": null, "options": []}, {"name": "repetition_penalty", "use_template": null, "label": {"zh_Hans": "重复惩罚", "en_US": "Repetition penalty"}, "type": "float", "help": {"zh_Hans": "用于控制模型生成时的重复度。提高repetition_penalty时可以降低模型生成的重复度。1.0表示不做惩罚。", "en_US": "Used to control the repeatability when generating models. Increasing repetition_penalty can reduce the duplication of model generation. 1.0 means no punishment."}, "required": false, "default": 1.1, "min": null, "max": null, "precision": null, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen2.5-Math-72B-Instruct", "label": {"zh_Hans": "Qwen/Qwen2.5-Math-72B-Instruct", "en_US": "Qwen/Qwen2.5-Math-72B-Instruct"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 4096, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "用于控制随机性和多样性的程度。具体来说，temperature值控制了生成文本时对每个候选词的概率分布进行平滑的程度。较高的temperature值会降低概率分布的峰值，使得更多的低概率词被选择，生成结果更加多样化；而较低的temperature值则会增强概率分布的峰值，使得高概率词更容易被选择，生成结果更加确定。", "en_US": "Used to control the degree of randomness and diversity. Specifically, the temperature value controls the degree to which the probability distribution of each candidate word is smoothed when generating text. A higher temperature value will reduce the peak value of the probability distribution, allowing more low-probability words to be selected, and the generated results will be more diverse; while a lower temperature value will enhance the peak value of the probability distribution, making it easier for high-probability words to be selected. , the generated results are more certain."}, "required": false, "default": 0.3, "min": 0.0, "max": 2.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "用于指定模型在生成内容时token的最大数量，它定义了生成的上限，但不保证每次都会生成到这个数量。", "en_US": "It is used to specify the maximum number of tokens when the model generates content. It defines the upper limit of generation, but does not guarantee that this number will be generated every time."}, "required": false, "default": 2000, "min": 1.0, "max": 2000.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值范围为（0,1.0)，取值越大，生成的随机性越高；取值越低，生成的确定性越高。", "en_US": "The probability threshold of the kernel sampling method during the generation process. For example, when the value is 0.8, only the smallest set of the most likely tokens with a sum of probabilities greater than or equal to 0.8 is retained as the candidate set. The value range is (0,1.0). The larger the value, the higher the randomness generated; the lower the value, the higher the certainty generated."}, "required": false, "default": 0.8, "min": 0.1, "max": 0.9, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "生成时，采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高。", "en_US": "The size of the sample candidate set when generated. For example, when the value is 50, only the 50 highest-scoring tokens in a single generation form a randomly sampled candidate set. The larger the value, the higher the randomness generated; the smaller the value, the higher the certainty generated."}, "required": false, "default": null, "min": 0.0, "max": 99.0, "precision": null, "options": []}, {"name": "seed", "use_template": null, "label": {"zh_Hans": "随机种子", "en_US": "Random seed"}, "type": "int", "help": {"zh_Hans": "生成时使用的随机数种子，用户控制模型生成内容的随机性。支持无符号64位整数，默认值为 1234。在使用seed时，模型将尽可能生成相同或相似的结果，但目前不保证每次生成的结果完全相同。", "en_US": "The random number seed used when generating, the user controls the randomness of the content generated by the model. Supports unsigned 64-bit integers, default value is 1234. When using seed, the model will try its best to generate the same or similar results, but there is currently no guarantee that the results will be exactly the same every time."}, "required": false, "default": 1234, "min": null, "max": null, "precision": null, "options": []}, {"name": "repetition_penalty", "use_template": null, "label": {"zh_Hans": "重复惩罚", "en_US": "Repetition penalty"}, "type": "float", "help": {"zh_Hans": "用于控制模型生成时的重复度。提高repetition_penalty时可以降低模型生成的重复度。1.0表示不做惩罚。", "en_US": "Used to control the repeatability when generating models. Increasing repetition_penalty can reduce the duplication of model generation. 1.0 means no punishment."}, "required": false, "default": 1.1, "min": null, "max": null, "precision": null, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "4.13", "output": "4.13", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen2.5-VL-32B-Instruct", "label": {"zh_Hans": "Qwen/Qwen2.5-VL-32B-Instruct", "en_US": "Qwen/Qwen2.5-VL-32B-Instruct"}, "model_type": "llm", "features": ["vision"], "fetch_from": "predefined-model", "model_properties": {"context_size": 131072, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "用于控制随机性和多样性的程度。具体来说，temperature值控制了生成文本时对每个候选词的概率分布进行平滑的程度。较高的temperature值会降低概率分布的峰值，使得更多的低概率词被选择，生成结果更加多样化；而较低的temperature值则会增强概率分布的峰值，使得高概率词更容易被选择，生成结果更加确定。", "en_US": "Used to control the degree of randomness and diversity. Specifically, the temperature value controls the degree to which the probability distribution of each candidate word is smoothed when generating text. A higher temperature value will reduce the peak value of the probability distribution, allowing more low-probability words to be selected, and the generated results will be more diverse; while a lower temperature value will enhance the peak value of the probability distribution, making it easier for high-probability words to be selected. , the generated results are more certain."}, "required": false, "default": 0.3, "min": 0.0, "max": 2.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "用于指定模型在生成内容时token的最大数量，它定义了生成的上限，但不保证每次都会生成到这个数量。", "en_US": "It is used to specify the maximum number of tokens when the model generates content. It defines the upper limit of generation, but does not guarantee that this number will be generated every time."}, "required": false, "default": 2000, "min": 1.0, "max": 2000.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值范围为（0,1.0)，取值越大，生成的随机性越高；取值越低，生成的确定性越高。", "en_US": "The probability threshold of the kernel sampling method during the generation process. For example, when the value is 0.8, only the smallest set of the most likely tokens with a sum of probabilities greater than or equal to 0.8 is retained as the candidate set. The value range is (0,1.0). The larger the value, the higher the randomness generated; the lower the value, the higher the certainty generated."}, "required": false, "default": 0.8, "min": 0.1, "max": 0.9, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "生成时，采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高。", "en_US": "The size of the sample candidate set when generated. For example, when the value is 50, only the 50 highest-scoring tokens in a single generation form a randomly sampled candidate set. The larger the value, the higher the randomness generated; the smaller the value, the higher the certainty generated."}, "required": false, "default": null, "min": 0.0, "max": 99.0, "precision": null, "options": []}, {"name": "seed", "use_template": null, "label": {"zh_Hans": "随机种子", "en_US": "Random seed"}, "type": "int", "help": {"zh_Hans": "生成时使用的随机数种子，用户控制模型生成内容的随机性。支持无符号64位整数，默认值为 1234。在使用seed时，模型将尽可能生成相同或相似的结果，但目前不保证每次生成的结果完全相同。", "en_US": "The random number seed used when generating, the user controls the randomness of the content generated by the model. Supports unsigned 64-bit integers, default value is 1234. When using seed, the model will try its best to generate the same or similar results, but there is currently no guarantee that the results will be exactly the same every time."}, "required": false, "default": 1234, "min": null, "max": null, "precision": null, "options": []}, {"name": "repetition_penalty", "use_template": null, "label": {"zh_Hans": "重复惩罚", "en_US": "Repetition penalty"}, "type": "float", "help": {"zh_Hans": "用于控制模型生成时的重复度。提高repetition_penalty时可以降低模型生成的重复度。1.0表示不做惩罚。", "en_US": "Used to control the repeatability when generating models. Increasing repetition_penalty can reduce the duplication of model generation. 1.0 means no punishment."}, "required": false, "default": 1.1, "min": null, "max": null, "precision": null, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "1.89", "output": "1.89", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen2.5-VL-72B-Instruct", "label": {"zh_Hans": "Qwen/Qwen2.5-VL-72B-Instruct", "en_US": "Qwen/Qwen2.5-VL-72B-Instruct"}, "model_type": "llm", "features": ["vision"], "fetch_from": "predefined-model", "model_properties": {"context_size": 131072, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "用于控制随机性和多样性的程度。具体来说，temperature值控制了生成文本时对每个候选词的概率分布进行平滑的程度。较高的temperature值会降低概率分布的峰值，使得更多的低概率词被选择，生成结果更加多样化；而较低的temperature值则会增强概率分布的峰值，使得高概率词更容易被选择，生成结果更加确定。", "en_US": "Used to control the degree of randomness and diversity. Specifically, the temperature value controls the degree to which the probability distribution of each candidate word is smoothed when generating text. A higher temperature value will reduce the peak value of the probability distribution, allowing more low-probability words to be selected, and the generated results will be more diverse; while a lower temperature value will enhance the peak value of the probability distribution, making it easier for high-probability words to be selected. , the generated results are more certain."}, "required": false, "default": 0.3, "min": 0.0, "max": 2.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "用于指定模型在生成内容时token的最大数量，它定义了生成的上限，但不保证每次都会生成到这个数量。", "en_US": "It is used to specify the maximum number of tokens when the model generates content. It defines the upper limit of generation, but does not guarantee that this number will be generated every time."}, "required": false, "default": 2000, "min": 1.0, "max": 2000.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值范围为（0,1.0)，取值越大，生成的随机性越高；取值越低，生成的确定性越高。", "en_US": "The probability threshold of the kernel sampling method during the generation process. For example, when the value is 0.8, only the smallest set of the most likely tokens with a sum of probabilities greater than or equal to 0.8 is retained as the candidate set. The value range is (0,1.0). The larger the value, the higher the randomness generated; the lower the value, the higher the certainty generated."}, "required": false, "default": 0.8, "min": 0.1, "max": 0.9, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "生成时，采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高。", "en_US": "The size of the sample candidate set when generated. For example, when the value is 50, only the 50 highest-scoring tokens in a single generation form a randomly sampled candidate set. The larger the value, the higher the randomness generated; the smaller the value, the higher the certainty generated."}, "required": false, "default": null, "min": 0.0, "max": 99.0, "precision": null, "options": []}, {"name": "seed", "use_template": null, "label": {"zh_Hans": "随机种子", "en_US": "Random seed"}, "type": "int", "help": {"zh_Hans": "生成时使用的随机数种子，用户控制模型生成内容的随机性。支持无符号64位整数，默认值为 1234。在使用seed时，模型将尽可能生成相同或相似的结果，但目前不保证每次生成的结果完全相同。", "en_US": "The random number seed used when generating, the user controls the randomness of the content generated by the model. Supports unsigned 64-bit integers, default value is 1234. When using seed, the model will try its best to generate the same or similar results, but there is currently no guarantee that the results will be exactly the same every time."}, "required": false, "default": 1234, "min": null, "max": null, "precision": null, "options": []}, {"name": "repetition_penalty", "use_template": null, "label": {"zh_Hans": "重复惩罚", "en_US": "Repetition penalty"}, "type": "float", "help": {"zh_Hans": "用于控制模型生成时的重复度。提高repetition_penalty时可以降低模型生成的重复度。1.0表示不做惩罚。", "en_US": "Used to control the repeatability when generating models. Increasing repetition_penalty can reduce the duplication of model generation. 1.0 means no punishment."}, "required": false, "default": 1.1, "min": null, "max": null, "precision": null, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "4.13", "output": "4.13", "unit": "0.000001", "currency": "RMB"}}, {"model": "Pro/Qwen/Qwen2.5-VL-7B-Instruct", "label": {"zh_Hans": "Pro/Qwen/Qwen2.5-VL-7B-Instruct", "en_US": "Pro/Qwen/Qwen2.5-VL-7B-Instruct"}, "model_type": "llm", "features": ["vision"], "fetch_from": "predefined-model", "model_properties": {"context_size": 32768, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "用于控制随机性和多样性的程度。具体来说，temperature值控制了生成文本时对每个候选词的概率分布进行平滑的程度。较高的temperature值会降低概率分布的峰值，使得更多的低概率词被选择，生成结果更加多样化；而较低的temperature值则会增强概率分布的峰值，使得高概率词更容易被选择，生成结果更加确定。", "en_US": "Used to control the degree of randomness and diversity. Specifically, the temperature value controls the degree to which the probability distribution of each candidate word is smoothed when generating text. A higher temperature value will reduce the peak value of the probability distribution, allowing more low-probability words to be selected, and the generated results will be more diverse; while a lower temperature value will enhance the peak value of the probability distribution, making it easier for high-probability words to be selected. , the generated results are more certain."}, "required": false, "default": 0.3, "min": 0.0, "max": 2.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "用于指定模型在生成内容时token的最大数量，它定义了生成的上限，但不保证每次都会生成到这个数量。", "en_US": "It is used to specify the maximum number of tokens when the model generates content. It defines the upper limit of generation, but does not guarantee that this number will be generated every time."}, "required": false, "default": 2000, "min": 1.0, "max": 2000.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值范围为（0,1.0)，取值越大，生成的随机性越高；取值越低，生成的确定性越高。", "en_US": "The probability threshold of the kernel sampling method during the generation process. For example, when the value is 0.8, only the smallest set of the most likely tokens with a sum of probabilities greater than or equal to 0.8 is retained as the candidate set. The value range is (0,1.0). The larger the value, the higher the randomness generated; the lower the value, the higher the certainty generated."}, "required": false, "default": 0.8, "min": 0.1, "max": 0.9, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "生成时，采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高。", "en_US": "The size of the sample candidate set when generated. For example, when the value is 50, only the 50 highest-scoring tokens in a single generation form a randomly sampled candidate set. The larger the value, the higher the randomness generated; the smaller the value, the higher the certainty generated."}, "required": false, "default": null, "min": 0.0, "max": 99.0, "precision": null, "options": []}, {"name": "seed", "use_template": null, "label": {"zh_Hans": "随机种子", "en_US": "Random seed"}, "type": "int", "help": {"zh_Hans": "生成时使用的随机数种子，用户控制模型生成内容的随机性。支持无符号64位整数，默认值为 1234。在使用seed时，模型将尽可能生成相同或相似的结果，但目前不保证每次生成的结果完全相同。", "en_US": "The random number seed used when generating, the user controls the randomness of the content generated by the model. Supports unsigned 64-bit integers, default value is 1234. When using seed, the model will try its best to generate the same or similar results, but there is currently no guarantee that the results will be exactly the same every time."}, "required": false, "default": 1234, "min": null, "max": null, "precision": null, "options": []}, {"name": "repetition_penalty", "use_template": null, "label": {"zh_Hans": "重复惩罚", "en_US": "Repetition penalty"}, "type": "float", "help": {"zh_Hans": "用于控制模型生成时的重复度。提高repetition_penalty时可以降低模型生成的重复度。1.0表示不做惩罚。", "en_US": "Used to control the repeatability when generating models. Increasing repetition_penalty can reduce the duplication of model generation. 1.0 means no punishment."}, "required": false, "default": 1.1, "min": null, "max": null, "precision": null, "options": []}, {"name": "response_format", "use_template": null, "label": {"zh_Hans": "回复格式", "en_US": "Response Format"}, "type": "string", "help": {"zh_Hans": "指定模型必须输出的格式", "en_US": "specifying the format that the model must output"}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": ["text", "json_object"]}], "pricing": {"input": "0.35", "output": "0.35", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen3-14B", "label": {"zh_Hans": "Qwen/Qwen3-14B", "en_US": "Qwen/Qwen3-14B"}, "model_type": "llm", "features": ["agent-thought", "multi-tool-call", "stream-tool-call"], "fetch_from": "predefined-model", "model_properties": {"context_size": 128000, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0.6, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 0.95, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 8192, "min": 1.0, "max": 8192.0, "precision": 0, "options": []}, {"name": "enable_thinking", "use_template": null, "label": {"zh_Hans": "思考模式", "en_US": "Thinking mode"}, "type": "boolean", "help": {"zh_Hans": "是否开启思考模式。", "en_US": "Whether to enable thinking mode."}, "required": false, "default": true, "min": null, "max": null, "precision": null, "options": []}, {"name": "thinking_budget", "use_template": null, "label": {"zh_Hans": "思考长度限制", "en_US": "Thinking budget"}, "type": "int", "help": {"zh_Hans": "思考过程的最大长度，只在思考模式为true时生效。", "en_US": "The maximum length of the thinking process, only effective when thinking mode is true."}, "required": false, "default": 512, "min": 1.0, "max": 8192.0, "precision": null, "options": []}], "pricing": {"input": "0.5", "output": "2", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen3-235B-A22B", "label": {"zh_Hans": "Qwen/Qwen3-235B-A22B", "en_US": "Qwen/Qwen3-235B-A22B"}, "model_type": "llm", "features": ["agent-thought", "multi-tool-call", "stream-tool-call"], "fetch_from": "predefined-model", "model_properties": {"context_size": 128000, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0.6, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 0.95, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 8192, "min": 1.0, "max": 8192.0, "precision": 0, "options": []}, {"name": "enable_thinking", "use_template": null, "label": {"zh_Hans": "思考模式", "en_US": "Thinking mode"}, "type": "boolean", "help": {"zh_Hans": "是否开启思考模式。", "en_US": "Whether to enable thinking mode."}, "required": false, "default": true, "min": null, "max": null, "precision": null, "options": []}, {"name": "thinking_budget", "use_template": null, "label": {"zh_Hans": "思考长度限制", "en_US": "Thinking budget"}, "type": "int", "help": {"zh_Hans": "思考过程的最大长度，只在思考模式为true时生效。", "en_US": "The maximum length of the thinking process, only effective when thinking mode is true."}, "required": false, "default": 512, "min": 1.0, "max": 8192.0, "precision": null, "options": []}], "pricing": {"input": "2.5", "output": "10", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen3-30B-A3B", "label": {"zh_Hans": "Qwen/Qwen3-30B-A3B", "en_US": "Qwen/Qwen3-30B-A3B"}, "model_type": "llm", "features": ["agent-thought", "multi-tool-call", "stream-tool-call"], "fetch_from": "predefined-model", "model_properties": {"context_size": 128000, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0.6, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 0.95, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 8192, "min": 1.0, "max": 8192.0, "precision": 0, "options": []}, {"name": "enable_thinking", "use_template": null, "label": {"zh_Hans": "思考模式", "en_US": "Thinking mode"}, "type": "boolean", "help": {"zh_Hans": "是否开启思考模式。", "en_US": "Whether to enable thinking mode."}, "required": false, "default": true, "min": null, "max": null, "precision": null, "options": []}, {"name": "thinking_budget", "use_template": null, "label": {"zh_Hans": "思考长度限制", "en_US": "Thinking budget"}, "type": "int", "help": {"zh_Hans": "思考过程的最大长度，只在思考模式为true时生效。", "en_US": "The maximum length of the thinking process, only effective when thinking mode is true."}, "required": false, "default": 512, "min": 1.0, "max": 8192.0, "precision": null, "options": []}], "pricing": {"input": "0.7", "output": "2.8", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen3-32B", "label": {"zh_Hans": "Qwen/Qwen3-32B", "en_US": "Qwen/Qwen3-32B"}, "model_type": "llm", "features": ["agent-thought", "multi-tool-call", "stream-tool-call"], "fetch_from": "predefined-model", "model_properties": {"context_size": 128000, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0.6, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 0.95, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 8192, "min": 1.0, "max": 8192.0, "precision": 0, "options": []}, {"name": "enable_thinking", "use_template": null, "label": {"zh_Hans": "思考模式", "en_US": "Thinking mode"}, "type": "boolean", "help": {"zh_Hans": "是否开启思考模式。", "en_US": "Whether to enable thinking mode."}, "required": false, "default": true, "min": null, "max": null, "precision": null, "options": []}, {"name": "thinking_budget", "use_template": null, "label": {"zh_Hans": "思考长度限制", "en_US": "Thinking budget"}, "type": "int", "help": {"zh_Hans": "思考过程的最大长度，只在思考模式为true时生效。", "en_US": "The maximum length of the thinking process, only effective when thinking mode is true."}, "required": false, "default": 512, "min": 1.0, "max": 8192.0, "precision": null, "options": []}], "pricing": {"input": "1", "output": "4", "unit": "0.000001", "currency": "RMB"}}, {"model": "Qwen/Qwen3-8B", "label": {"zh_Hans": "Qwen/Qwen3-8B", "en_US": "Qwen/Qwen3-8B"}, "model_type": "llm", "features": ["agent-thought", "multi-tool-call", "stream-tool-call"], "fetch_from": "predefined-model", "model_properties": {"context_size": 128000, "mode": "chat"}, "deprecated": false, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0.6, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 0.95, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 8192, "min": 1.0, "max": 8192.0, "precision": 0, "options": []}, {"name": "enable_thinking", "use_template": null, "label": {"zh_Hans": "思考模式", "en_US": "Thinking mode"}, "type": "boolean", "help": {"zh_Hans": "是否开启思考模式。", "en_US": "Whether to enable thinking mode."}, "required": false, "default": true, "min": null, "max": null, "precision": null, "options": []}, {"name": "thinking_budget", "use_template": null, "label": {"zh_Hans": "思考长度限制", "en_US": "Thinking budget"}, "type": "int", "help": {"zh_Hans": "思考过程的最大长度，只在思考模式为true时生效。", "en_US": "The maximum length of the thinking process, only effective when thinking mode is true."}, "required": false, "default": 512, "min": 1.0, "max": 8192.0, "precision": null, "options": []}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "01-ai/Yi-1.5-34B-<PERSON><PERSON>", "label": {"zh_Hans": "01-ai/Yi-1.5-34B-Chat-16K", "en_US": "01-ai/Yi-1.5-34B-Chat-16K"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 16384, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}], "pricing": {"input": "1.26", "output": "1.26", "unit": "0.000001", "currency": "RMB"}}, {"model": "01-ai/Yi-1.5-6B-<PERSON><PERSON>", "label": {"zh_Hans": "01-ai/Yi-1.5-6B-<PERSON><PERSON>", "en_US": "01-ai/Yi-1.5-6B-<PERSON><PERSON>"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 4096, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "01-ai/Yi-1.5-9B-Chat-16K", "label": {"zh_Hans": "01-ai/Yi-1.5-9B-Chat-16K", "en_US": "01-ai/Yi-1.5-9B-Chat-16K"}, "model_type": "llm", "features": ["agent-thought"], "fetch_from": "predefined-model", "model_properties": {"context_size": 16384, "mode": "chat"}, "deprecated": true, "parameter_rules": [{"name": "temperature", "use_template": "temperature", "label": {"zh_Hans": "temperature", "en_US": "temperature"}, "type": "float", "help": {"zh_Hans": "温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。", "en_US": "Controls randomness. Lower temperature results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive. Higher temperature results in more random completions."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "max_tokens", "use_template": "max_tokens", "label": {"zh_Hans": "max_tokens", "en_US": "max_tokens"}, "type": "int", "help": {"zh_Hans": "指定生成结果长度的上限。如果生成结果截断，可以调大该参数。", "en_US": "Specifies the upper limit on the length of generated results. If the generated results are truncated, you can increase this parameter."}, "required": false, "default": 512, "min": 1.0, "max": 4096.0, "precision": 0, "options": []}, {"name": "top_p", "use_template": "top_p", "label": {"zh_Hans": "top_p", "en_US": "top_p"}, "type": "float", "help": {"zh_Hans": "通过核心采样控制多样性：0.5表示考虑了一半的所有可能性加权选项。", "en_US": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "required": false, "default": 1, "min": 0.0, "max": 1.0, "precision": 2, "options": []}, {"name": "top_k", "use_template": null, "label": {"zh_Hans": "取样数量", "en_US": "Top k"}, "type": "int", "help": {"zh_Hans": "仅从每个后续标记的前 K 个选项中采样。", "en_US": "Only sample from the top K options for each subsequent token."}, "required": false, "default": null, "min": null, "max": null, "precision": null, "options": []}, {"name": "frequency_penalty", "use_template": "frequency_penalty", "label": {"zh_Hans": "frequency_penalty", "en_US": "frequency_penalty"}, "type": "float", "help": {"zh_Hans": "对文本中出现的标记的对数概率施加惩罚。", "en_US": "Applies a penalty to the log-probability of tokens that appear in the text."}, "required": false, "default": 0, "min": 0.0, "max": 1.0, "precision": 2, "options": []}], "pricing": {"input": "0", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "netease-youdao/bce-reranker-base_v1", "label": {"zh_Hans": "netease-youdao/bce-reranker-base_v1", "en_US": "netease-youdao/bce-reranker-base_v1"}, "model_type": "rerank", "features": null, "fetch_from": "predefined-model", "model_properties": {"context_size": 512}, "deprecated": false, "parameter_rules": [], "pricing": null}, {"model": "BAAI/bge-reranker-v2-m3", "label": {"zh_Hans": "BAAI/bge-reranker-v2-m3", "en_US": "BAAI/bge-reranker-v2-m3"}, "model_type": "rerank", "features": null, "fetch_from": "predefined-model", "model_properties": {"context_size": 8192}, "deprecated": false, "parameter_rules": [], "pricing": null}, {"model": "FunAudioLLM/SenseVoiceSmall", "label": {"zh_Hans": "FunAudioLLM/SenseVoiceSmall", "en_US": "FunAudioLLM/SenseVoiceSmall"}, "model_type": "speech2text", "features": null, "fetch_from": "predefined-model", "model_properties": {"file_upload_limit": 1, "supported_file_extensions": "mp3,wav"}, "deprecated": false, "parameter_rules": [], "pricing": null}, {"model": "iic/SenseVoiceSmall", "label": {"zh_Hans": "iic/SenseVoiceSmall", "en_US": "iic/SenseVoiceSmall"}, "model_type": "speech2text", "features": null, "fetch_from": "predefined-model", "model_properties": {"file_upload_limit": 1, "supported_file_extensions": "mp3,wav"}, "deprecated": true, "parameter_rules": [], "pricing": null}, {"model": "netease-youdao/bce-embedding-base_v1", "label": {"zh_Hans": "netease-youdao/bce-embedding-base_v1", "en_US": "netease-youdao/bce-embedding-base_v1"}, "model_type": "text-embedding", "features": null, "fetch_from": "predefined-model", "model_properties": {"context_size": 512, "max_chunks": 1}, "deprecated": false, "parameter_rules": [], "pricing": null}, {"model": "BAAI/bge-large-en-v1.5", "label": {"zh_Hans": "BAAI/bge-large-en-v1.5", "en_US": "BAAI/bge-large-en-v1.5"}, "model_type": "text-embedding", "features": null, "fetch_from": "predefined-model", "model_properties": {"context_size": 512, "max_chunks": 1}, "deprecated": false, "parameter_rules": [], "pricing": null}, {"model": "BAAI/bge-large-zh-v1.5", "label": {"zh_Hans": "BAAI/bge-large-zh-v1.5", "en_US": "BAAI/bge-large-zh-v1.5"}, "model_type": "text-embedding", "features": null, "fetch_from": "predefined-model", "model_properties": {"context_size": 512, "max_chunks": 1}, "deprecated": false, "parameter_rules": [], "pricing": null}, {"model": "BAAI/bge-m3", "label": {"zh_Hans": "BAAI/bge-m3", "en_US": "BAAI/bge-m3"}, "model_type": "text-embedding", "features": null, "fetch_from": "predefined-model", "model_properties": {"context_size": 8192, "max_chunks": 1}, "deprecated": false, "parameter_rules": [], "pricing": null}, {"model": "FunAudioLLM/CosyVoice2-0.5B", "label": {"zh_Hans": "FunAudioLLM/CosyVoice2-0.5B", "en_US": "FunAudioLLM/CosyVoice2-0.5B"}, "model_type": "tts", "features": null, "fetch_from": "predefined-model", "model_properties": {"audio_type": "mp3", "default_voice": "FunAudioLLM/CosyVoice2-0.5B:alex", "max_workers": 5, "voices": [{"language": ["zh-Hans", "en-US"], "mode": "FunAudioLLM/CosyVoice2-0.5B:alex", "name": "<PERSON>（男声）"}, {"language": ["zh-Hans", "en-US"], "mode": "FunAudioLLM/CosyVoice2-0.5B:benjamin", "name": "<PERSON>（男声）"}, {"language": ["zh-Hans", "en-US"], "mode": "FunAudioLLM/CosyVoice2-0.5B:charles", "name": "<PERSON>（男声）"}, {"language": ["zh-Hans", "en-US"], "mode": "FunAudioLLM/CosyVoice2-0.5B:david", "name": "<PERSON>（男声）"}, {"language": ["zh-Hans", "en-US"], "mode": "FunAudioLLM/CosyVoice2-0.5B:anna", "name": "<PERSON>（女声）"}, {"language": ["zh-Hans", "en-US"], "mode": "FunAudioLLM/CosyVoice2-0.5B:bella", "name": "<PERSON>（女声）"}, {"language": ["zh-Hans", "en-US"], "mode": "FunAudioLLM/CosyVoice2-0.5B:claire", "name": "<PERSON>（女声）"}, {"language": ["zh-Hans", "en-US"], "mode": "FunAudioLLM/CosyVoice2-0.5B:diana", "name": "<PERSON>（女声）"}]}, "deprecated": false, "parameter_rules": [], "pricing": {"input": "50", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "fishaudio/fish-speech-1.4", "label": {"zh_Hans": "fishaudio/fish-speech-1.4", "en_US": "fishaudio/fish-speech-1.4"}, "model_type": "tts", "features": null, "fetch_from": "predefined-model", "model_properties": {"audio_type": "mp3", "default_voice": "fishaudio/fish-speech-1.4:alex", "max_workers": 5, "voices": [{"language": ["zh-Hans", "en-US"], "mode": "fishaudio/fish-speech-1.4:alex", "name": "<PERSON>（男声）"}, {"language": ["zh-Hans", "en-US"], "mode": "fishaudio/fish-speech-1.4:benjamin", "name": "<PERSON>（男声）"}, {"language": ["zh-Hans", "en-US"], "mode": "fishaudio/fish-speech-1.4:charles", "name": "<PERSON>（男声）"}, {"language": ["zh-Hans", "en-US"], "mode": "fishaudio/fish-speech-1.4:david", "name": "<PERSON>（男声）"}, {"language": ["zh-Hans", "en-US"], "mode": "fishaudio/fish-speech-1.4:anna", "name": "<PERSON>（女声）"}, {"language": ["zh-Hans", "en-US"], "mode": "fishaudio/fish-speech-1.4:bella", "name": "<PERSON>（女声）"}, {"language": ["zh-Hans", "en-US"], "mode": "fishaudio/fish-speech-1.4:claire", "name": "<PERSON>（女声）"}, {"language": ["zh-Hans", "en-US"], "mode": "fishaudio/fish-speech-1.4:diana", "name": "<PERSON>（女声）"}]}, "deprecated": true, "parameter_rules": [], "pricing": {"input": "105", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "fishaudio/fish-speech-1.5", "label": {"zh_Hans": "fishaudio/fish-speech-1.5", "en_US": "fishaudio/fish-speech-1.5"}, "model_type": "tts", "features": null, "fetch_from": "predefined-model", "model_properties": {"audio_type": "mp3", "default_voice": "fishaudio/fish-speech-1.5:alex", "max_workers": 5, "voices": [{"language": ["zh-Hans", "en-US"], "mode": "fishaudio/fish-speech-1.5:alex", "name": "<PERSON>（男声）"}, {"language": ["zh-Hans", "en-US"], "mode": "fishaudio/fish-speech-1.5:benjamin", "name": "<PERSON>（男声）"}, {"language": ["zh-Hans", "en-US"], "mode": "fishaudio/fish-speech-1.5:charles", "name": "<PERSON>（男声）"}, {"language": ["zh-Hans", "en-US"], "mode": "fishaudio/fish-speech-1.5:david", "name": "<PERSON>（男声）"}, {"language": ["zh-Hans", "en-US"], "mode": "fishaudio/fish-speech-1.5:anna", "name": "<PERSON>（女声）"}, {"language": ["zh-Hans", "en-US"], "mode": "fishaudio/fish-speech-1.5:bella", "name": "<PERSON>（女声）"}, {"language": ["zh-Hans", "en-US"], "mode": "fishaudio/fish-speech-1.5:claire", "name": "<PERSON>（女声）"}, {"language": ["zh-Hans", "en-US"], "mode": "fishaudio/fish-speech-1.5:diana", "name": "<PERSON>（女声）"}]}, "deprecated": true, "parameter_rules": [], "pricing": {"input": "105", "output": "0", "unit": "0.000001", "currency": "RMB"}}, {"model": "RVC-Boss/GPT-SoVITS", "label": {"zh_Hans": "RVC-Boss/GPT-SoVITS", "en_US": "RVC-Boss/GPT-SoVITS"}, "model_type": "tts", "features": null, "fetch_from": "predefined-model", "model_properties": {"audio_type": "mp3", "default_voice": "RVC-Boss/GPT-SoVITS:alex", "max_workers": 5, "voices": [{"language": ["zh-Hans", "en-US"], "mode": "RVC-Boss/GPT-SoVITS:alex", "name": "<PERSON>（男声）"}, {"language": ["zh-Hans", "en-US"], "mode": "RVC-Boss/GPT-SoVITS:benjamin", "name": "<PERSON>（男声）"}, {"language": ["zh-Hans", "en-US"], "mode": "RVC-Boss/GPT-SoVITS:charles", "name": "<PERSON>（男声）"}, {"language": ["zh-Hans", "en-US"], "mode": "RVC-Boss/GPT-SoVITS:david", "name": "<PERSON>（男声）"}, {"language": ["zh-Hans", "en-US"], "mode": "RVC-Boss/GPT-SoVITS:anna", "name": "<PERSON>（女声）"}, {"language": ["zh-Hans", "en-US"], "mode": "RVC-Boss/GPT-SoVITS:bella", "name": "<PERSON>（女声）"}, {"language": ["zh-Hans", "en-US"], "mode": "RVC-Boss/GPT-SoVITS:claire", "name": "<PERSON>（女声）"}, {"language": ["zh-Hans", "en-US"], "mode": "RVC-Boss/GPT-SoVITS:diana", "name": "<PERSON>（女声）"}]}, "deprecated": true, "parameter_rules": [], "pricing": {"input": "50", "output": "0", "unit": "0.000001", "currency": "RMB"}}], "provider_credential_schema": {"credential_form_schemas": [{"variable": "api_key", "label": {"zh_Hans": "API Key", "en_US": "API Key"}, "type": "secret-input", "required": true, "default": null, "options": [], "placeholder": {"zh_Hans": "在此输入您的 API Key", "en_US": "Enter your API Key"}, "max_length": 0, "show_on": []}]}, "model_credential_schema": {"model": {"label": {"zh_Hans": "模型名称", "en_US": "Model Name"}, "placeholder": {"zh_Hans": "输入模型名称", "en_US": "Enter your model name"}}, "credential_form_schemas": [{"variable": "api_key", "label": {"zh_Hans": "API Key", "en_US": "API Key"}, "type": "secret-input", "required": true, "default": null, "options": [], "placeholder": {"zh_Hans": "在此输入您的 API Key", "en_US": "Enter your API Key"}, "max_length": 0, "show_on": []}, {"variable": "context_size", "label": {"zh_Hans": "模型上下文长度", "en_US": "Model context size"}, "type": "text-input", "required": true, "default": "4096", "options": [], "placeholder": {"zh_Hans": "在此输入您的模型上下文长度", "en_US": "Enter your Model context size"}, "max_length": 0, "show_on": []}, {"variable": "max_tokens", "label": {"zh_Hans": "最大 token 上限", "en_US": "Upper bound for max tokens"}, "type": "text-input", "required": false, "default": "4096", "options": [], "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "function_calling_type", "label": {"zh_Hans": "Function calling", "en_US": "Function calling"}, "type": "select", "required": false, "default": "no_call", "options": [{"label": {"zh_Hans": "不支持", "en_US": "Not Support"}, "value": "no_call", "show_on": []}, {"label": {"zh_Hans": "支持", "en_US": "Support"}, "value": "function_call", "show_on": []}], "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}]}, "position": {"llm": ["Pro/moonshotai/Kimi-K2-Instruct", "moonshotai/Kimi-K2-Instruct", "Pro/deepseek-ai/DeepSeek-R1", "Pro/deepseek-ai/DeepSeek-V3", "deepseek-ai/DeepSeek-R1", "deepseek-ai/DeepSeek-V3", "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B", "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B", "deepseek-ai/DeepSeek-R1-<PERSON>still-Qwen-7B", "deepseek-ai/DeepSeek-R1-Distill-Llama-70B", "deepseek-ai/DeepSeek-R1-Distill-Llama-8B", "Qwen/Qwen3-235B-A22B", "Qwen/Qwen3-32B", "Qwen/Qwen3-30B-A3B", "Qwen/Qwen3-14B", "Qwen/Qwen3-8B", "Qwen/Qwen2.5-72B-Instruct", "Qwen/Qwen2.5-72B-Instruct-128K", "Qwen/Qwen2.5-32B-Instruct", "Qwen/Qwen2.5-14B-Instruct", "Qwen/Qwen2.5-7B-Instruct", "Qwen/Qwen2.5-Coder-32B-Instruct", "Qwen/Qwen2.5-Coder-7B-Instruct", "Qwen/Qwen2.5-VL-72B-Instruct", "Qwen/Qwen2.5-VL-32B-Instruct", "Pro/Qwen/Qwen2.5-VL-7B-Instruct", "THUDM/GLM-4.1V-9B-Thinking", "THUDM/glm-4-9b-chat"]}}, "endpoint": null, "agent_strategy": null, "meta": {"minimum_dify_version": null}}, "name": "siliconflow", "installation_id": "e6e54dbe-09c3-4f09-8f7b-53d4733cdc11"}, {"id": "13c43134-2f07-4bbd-8ce4-91c2b9e24f68", "created_at": "2025-07-25T03:20:20.816294Z", "updated_at": "2025-07-25T03:20:20.816294Z", "tenant_id": "f24e8fa4-b522-48b2-a5ff-fe639b38d089", "endpoints_setups": 0, "endpoints_active": 0, "runtime_type": "local", "source": "marketplace", "meta": {"plugin_unique_identifier": "langgenius/wecom:0.0.3@7a90abb01f25de45d35f26b6bd57dd8d70e0e2c74703274df43d6b2f1c648603"}, "plugin_id": "langgenius/wecom", "plugin_unique_identifier": "langgenius/wecom:0.0.3@7a90abb01f25de45d35f26b6bd57dd8d70e0e2c74703274df43d6b2f1c648603", "version": "0.0.3", "checksum": "7a90abb01f25de45d35f26b6bd57dd8d70e0e2c74703274df43d6b2f1c648603", "declaration": {"version": "0.0.3", "author": "lang<PERSON><PERSON>", "name": "wecom", "description": {"en_US": "Wecom group bot", "zh_Hans": "企业微信群机器人", "pt_BR": "Wecom group bot", "ja_JP": "Wecom group bot"}, "icon": "0f7de80d305575eb2c8c3c1912209d7cfc5856b66eccb209b73ad769da68578e.png", "label": {"en_US": "Wecom", "zh_Hans": "企业微信", "pt_BR": "Wecom", "ja_JP": "Wecom"}, "category": "tool", "created_at": "2024-09-20T08:03:44.658609Z", "resource": {"memory": 1048576, "permission": {"tool": {"enabled": true}, "model": {"enabled": true, "llm": true, "text_embedding": false, "rerank": false, "tts": false, "speech2text": false, "moderation": false}, "node": null, "endpoint": null, "storage": null}}, "plugins": {"tools": ["provider/wecom.yaml"], "models": null, "endpoints": null}, "tags": ["social"], "repo": null, "verified": true, "tool": {"identity": {"author": "<PERSON>", "name": "wecom", "description": {"en_US": "Wecom group bot", "zh_Hans": "企业微信群机器人", "pt_BR": "Wecom group bot", "ja_JP": "Wecom group bot"}, "icon": "0f7de80d305575eb2c8c3c1912209d7cfc5856b66eccb209b73ad769da68578e.png", "label": {"en_US": "Wecom", "zh_Hans": "企业微信", "pt_BR": "Wecom", "ja_JP": "Wecom"}, "tags": ["social"]}, "plugin_id": "langgenius/wecom", "credentials_schema": []}, "model": null, "endpoint": null, "agent_strategy": null, "meta": {"minimum_dify_version": null}}, "name": "wecom", "installation_id": "13c43134-2f07-4bbd-8ce4-91c2b9e24f68"}, {"id": "eb8b48bb-000b-48a6-a6a7-86b67fd58cf9", "created_at": "2025-07-28T06:10:28.532864Z", "updated_at": "2025-07-28T06:10:28.532864Z", "tenant_id": "f24e8fa4-b522-48b2-a5ff-fe639b38d089", "endpoints_setups": 0, "endpoints_active": 0, "runtime_type": "local", "source": "package", "meta": {}, "plugin_id": "junjiem/mcp_compat_dify_tools", "plugin_unique_identifier": "junjiem/mcp_compat_dify_tools:0.1.1@e6e0ae3c135ab0edcc69d7d15fd950db8cd43ba398b415a56a0556a4f7b2ed05", "version": "0.1.1", "checksum": "e6e0ae3c135ab0edcc69d7d15fd950db8cd43ba398b415a56a0556a4f7b2ed05", "declaration": {"version": "0.1.1", "author": "jun<PERSON><PERSON>", "name": "mcp_compat_dify_tools", "description": {"en_US": "Convert your Dify tools's API to MCP compatible API (Note: must dify 1.2.0+)", "zh_Hans": "将您的Dify工具的API转换为MCP兼容API（注：必须 dify 1.2.0+）", "pt_BR": "Convert your Dify tools's API to MCP compatible API (Note: must dify 1.2.0+)", "ja_JP": "Convert your Dify tools's API to MCP compatible API (Note: must dify 1.2.0+)"}, "icon": "bbb4d283b602ca75be973c2bbd7a0121fb847f6d15aa8b06fd6de54fc37896fd.svg", "label": {"en_US": "MCP Compatible Dify Tools", "zh_Hans": "MCP Compatible Dify Tools", "pt_BR": "MCP Compatible Dify Tools", "ja_JP": "MCP Compatible Dify Tools"}, "category": "extension", "created_at": "2025-04-11T15:35:09.882892+08:00", "resource": {"memory": 268435456, "permission": {"tool": {"enabled": true}, "model": null, "node": null, "endpoint": {"enabled": true}, "storage": {"enabled": true, "size": 1048576}}}, "plugins": {"tools": null, "models": null, "endpoints": ["group/mcp_compat_dify_tools.yaml"]}, "tags": [], "repo": null, "verified": true, "tool": null, "model": null, "endpoint": {"settings": [{"type": "array[tools]", "name": "tools", "scope": null, "required": true, "default": null, "options": null, "label": {"en_US": "Tool list", "zh_Hans": "工具列表", "pt_BR": "Tool list", "ja_JP": "Tool list"}, "help": null, "url": null, "placeholder": null}], "endpoints": [{"path": "/sse", "method": "GET", "hidden": false}, {"path": "/messages/", "method": "POST", "hidden": false}, {"path": "/mcp", "method": "GET", "hidden": false}, {"path": "/mcp", "method": "POST", "hidden": false}]}, "agent_strategy": null, "meta": {"minimum_dify_version": null}}, "name": "mcp_compat_dify_tools", "installation_id": "eb8b48bb-000b-48a6-a6a7-86b67fd58cf9"}, {"id": "9cb49dbd-0020-442c-9112-fd9402cbde83", "created_at": "2025-07-28T06:49:17.981752Z", "updated_at": "2025-07-28T06:49:17.981752Z", "tenant_id": "f24e8fa4-b522-48b2-a5ff-fe639b38d089", "endpoints_setups": 0, "endpoints_active": 0, "runtime_type": "local", "source": "marketplace", "meta": {"plugin_unique_identifier": "langgenius/openai_api_compatible:0.0.19@219552f62b54919d6fd317c737956d3b2cc97719b85f0179bb995e5a512b7ebb"}, "plugin_id": "langgenius/openai_api_compatible", "plugin_unique_identifier": "langgenius/openai_api_compatible:0.0.19@219552f62b54919d6fd317c737956d3b2cc97719b85f0179bb995e5a512b7ebb", "version": "0.0.19", "checksum": "219552f62b54919d6fd317c737956d3b2cc97719b85f0179bb995e5a512b7ebb", "declaration": {"version": "0.0.19", "author": "lang<PERSON><PERSON>", "name": "openai_api_compatible", "description": {"en_US": "Model providers compatible with OpenAI's API standard, such as LM Studio.", "zh_Hans": "兼容 OpenAI API 的模型供应商，例如 LM Studio 。", "pt_BR": "Model providers compatible with OpenAI's API standard, such as LM Studio.", "ja_JP": "Model providers compatible with OpenAI's API standard, such as LM Studio."}, "icon": "fb1d7c05088e34ecabd0e087ebcfd929be9aad785b5e21e0577c2aa82acf7ae0.svg", "label": {"en_US": "OpenAI-API-compatible", "zh_Hans": "OpenAI-API-compatible", "pt_BR": "OpenAI-API-compatible", "ja_JP": "OpenAI-API-compatible"}, "category": "model", "created_at": "2024-07-12T08:03:44.658609Z", "resource": {"memory": 1048576, "permission": {"tool": {"enabled": true}, "model": {"enabled": true, "llm": true, "text_embedding": false, "rerank": false, "tts": false, "speech2text": false, "moderation": false}, "node": null, "endpoint": null, "storage": null}}, "plugins": {"tools": null, "models": ["provider/openai_api_compatible.yaml"], "endpoints": null}, "tags": [], "repo": null, "verified": true, "tool": null, "model": {"provider": "openai_api_compatible", "label": {"zh_Hans": "OpenAI-API-compatible", "en_US": "OpenAI-API-compatible"}, "description": {"zh_Hans": "兼容 OpenAI API 的模型供应商，例如 LM Studio 。", "en_US": "Model providers compatible with OpenAI's API standard, such as LM Studio."}, "icon_small": {"zh_Hans": "fb1d7c05088e34ecabd0e087ebcfd929be9aad785b5e21e0577c2aa82acf7ae0.svg", "en_US": "fb1d7c05088e34ecabd0e087ebcfd929be9aad785b5e21e0577c2aa82acf7ae0.svg"}, "icon_large": null, "background": null, "help": null, "supported_model_types": ["llm", "rerank", "text-embedding", "speech2text", "tts"], "configurate_methods": ["customizable-model"], "models": [], "provider_credential_schema": null, "model_credential_schema": {"model": {"label": {"zh_Hans": "模型名称", "en_US": "Model Name"}, "placeholder": {"zh_Hans": "输入模型全称", "en_US": "Enter full model name"}}, "credential_form_schemas": [{"variable": "display_name", "label": {"zh_Hans": "模型显示名称", "en_US": "Model display name"}, "type": "text-input", "required": false, "default": null, "options": [], "placeholder": {"zh_Hans": "模型在界面的显示名称", "en_US": "The display name of the model in the interface."}, "max_length": 0, "show_on": []}, {"variable": "api_key", "label": {"zh_Hans": "API Key", "en_US": "API Key"}, "type": "secret-input", "required": false, "default": null, "options": [], "placeholder": {"zh_Hans": "在此输入您的 API Key", "en_US": "Enter your API Key"}, "max_length": 0, "show_on": []}, {"variable": "endpoint_url", "label": {"zh_Hans": "API endpoint URL", "en_US": "API endpoint URL"}, "type": "text-input", "required": true, "default": null, "options": [], "placeholder": {"zh_Hans": "Base URL, e.g. https://api.openai.com/v1", "en_US": "Base URL, e.g. https://api.openai.com/v1"}, "max_length": 0, "show_on": []}, {"variable": "endpoint_model_name", "label": {"zh_Hans": "API endpoint中的模型名称", "en_US": "model name for API endpoint"}, "type": "text-input", "required": false, "default": null, "options": [], "placeholder": {"zh_Hans": "endpoint model name, e.g. chatgpt4.0", "en_US": "endpoint model name, e.g. chatgpt4.0"}, "max_length": 0, "show_on": []}, {"variable": "mode", "label": {"zh_Hans": "Completion mode", "en_US": "Completion mode"}, "type": "select", "required": false, "default": "chat", "options": [{"label": {"zh_Hans": "补全", "en_US": "Completion"}, "value": "completion", "show_on": []}, {"label": {"zh_Hans": "对话", "en_US": "Cha<PERSON>"}, "value": "chat", "show_on": []}], "placeholder": {"zh_Hans": "选择对话类型", "en_US": "Select completion mode"}, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "context_size", "label": {"zh_Hans": "模型上下文长度", "en_US": "Model context size"}, "type": "text-input", "required": true, "default": "4096", "options": [], "placeholder": {"zh_Hans": "在此输入您的模型上下文长度", "en_US": "Enter your Model context size"}, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "context_size", "label": {"zh_Hans": "模型上下文长度", "en_US": "Model context size"}, "type": "text-input", "required": true, "default": "4096", "options": [], "placeholder": {"zh_Hans": "在此输入您的模型上下文长度", "en_US": "Enter your Model context size"}, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "text-embedding"}]}, {"variable": "context_size", "label": {"zh_Hans": "模型上下文长度", "en_US": "Model context size"}, "type": "text-input", "required": true, "default": "4096", "options": [], "placeholder": {"zh_Hans": "在此输入您的模型上下文长度", "en_US": "Enter your Model context size"}, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "rerank"}]}, {"variable": "max_tokens_to_sample", "label": {"zh_Hans": "最大 token 上限", "en_US": "Upper bound for max tokens"}, "type": "text-input", "required": false, "default": "4096", "options": [], "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "agent_though_support", "label": {"zh_Hans": "Agent Thought", "en_US": "Agent Thought"}, "type": "select", "required": false, "default": "not_supported", "options": [{"label": {"zh_Hans": "支持", "en_US": "Support"}, "value": "supported", "show_on": []}, {"label": {"zh_Hans": "不支持", "en_US": "Not Support"}, "value": "not_supported", "show_on": []}], "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "function_calling_type", "label": {"zh_Hans": "Function calling", "en_US": "Function calling"}, "type": "select", "required": false, "default": "no_call", "options": [{"label": {"zh_Hans": "Function Call", "en_US": "Function Call"}, "value": "function_call", "show_on": []}, {"label": {"zh_Hans": "Tool Call", "en_US": "Tool Call"}, "value": "tool_call", "show_on": []}, {"label": {"zh_Hans": "不支持", "en_US": "Not Support"}, "value": "no_call", "show_on": []}], "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "stream_function_calling", "label": {"zh_Hans": "Stream function calling", "en_US": "Stream function calling"}, "type": "select", "required": false, "default": "not_supported", "options": [{"label": {"zh_Hans": "支持", "en_US": "Support"}, "value": "supported", "show_on": []}, {"label": {"zh_Hans": "不支持", "en_US": "Not Support"}, "value": "not_supported", "show_on": []}], "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "vision_support", "label": {"zh_Hans": "Vision 支持", "en_US": "Vision Support"}, "type": "select", "required": false, "default": "no_support", "options": [{"label": {"zh_Hans": "支持", "en_US": "Support"}, "value": "support", "show_on": []}, {"label": {"zh_Hans": "不支持", "en_US": "Not Support"}, "value": "no_support", "show_on": []}], "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "structured_output_support", "label": {"zh_Hans": "Structured Output", "en_US": "Structured Output"}, "type": "select", "required": false, "default": "not_supported", "options": [{"label": {"zh_Hans": "支持", "en_US": "Support"}, "value": "supported", "show_on": []}, {"label": {"zh_Hans": "不支持", "en_US": "Not Support"}, "value": "not_supported", "show_on": []}], "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "stream_mode_auth", "label": {"zh_Hans": "Stream mode auth", "en_US": "Stream mode auth"}, "type": "select", "required": false, "default": "not_use", "options": [{"label": {"zh_Hans": "使用", "en_US": "Use"}, "value": "use", "show_on": []}, {"label": {"zh_Hans": "不使用", "en_US": "Not Use"}, "value": "not_use", "show_on": []}], "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "stream_mode_delimiter", "label": {"zh_Hans": "流模式返回结果的分隔符", "en_US": "Delimiter for streaming results"}, "type": "text-input", "required": false, "default": "\\n\\n", "options": [], "placeholder": null, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "llm"}]}, {"variable": "voices", "label": {"zh_Hans": "可用声音（用英文逗号分隔）", "en_US": "Available Voices (comma-separated)"}, "type": "text-input", "required": false, "default": "alloy", "options": [], "placeholder": {"zh_Hans": "alloy,echo,fable,onyx,nova,shimmer", "en_US": "alloy,echo,fable,onyx,nova,shimmer"}, "max_length": 0, "show_on": [{"variable": "__model_type", "value": "tts"}]}]}, "position": {}}, "endpoint": null, "agent_strategy": null, "meta": {"minimum_dify_version": null}}, "name": "openai_api_compatible", "installation_id": "9cb49dbd-0020-442c-9112-fd9402cbde83"}, {"id": "e2467a85-86a0-4027-aebd-cf0ed262e2b5", "created_at": "2025-07-28T08:34:40.826583Z", "updated_at": "2025-07-28T08:34:40.826583Z", "tenant_id": "f24e8fa4-b522-48b2-a5ff-fe639b38d089", "endpoints_setups": 0, "endpoints_active": 0, "runtime_type": "local", "source": "marketplace", "meta": {"plugin_unique_identifier": "langgenius/wikipedia:0.0.3@63972698f033252b19a23a203f0beb6c3af16c6e8b0b886f001d902165a3da0c"}, "plugin_id": "langgenius/wikipedia", "plugin_unique_identifier": "langgenius/wikipedia:0.0.3@63972698f033252b19a23a203f0beb6c3af16c6e8b0b886f001d902165a3da0c", "version": "0.0.3", "checksum": "63972698f033252b19a23a203f0beb6c3af16c6e8b0b886f001d902165a3da0c", "declaration": {"version": "0.0.3", "author": "lang<PERSON><PERSON>", "name": "wikipedia", "description": {"en_US": "Wikipedia is a free online encyclopedia, created and edited by volunteers around the world.", "zh_Hans": "维基百科是一个由全世界的志愿者创建和编辑的免费在线百科全书。", "pt_BR": "Wikipedia is a free online encyclopedia, created and edited by volunteers around the world.", "ja_JP": "Wikipedia is a free online encyclopedia, created and edited by volunteers around the world."}, "icon": "a83ac9eac258f6710e51050e914e9b97d978fcbfc2b53923f4f320c8eef44424.svg", "label": {"en_US": "Wikipedia", "zh_Hans": "维基百科", "pt_BR": "Wikipedia", "ja_JP": "Wikipedia"}, "category": "tool", "created_at": "2024-09-20T08:03:44.658609Z", "resource": {"memory": 1048576, "permission": {"tool": {"enabled": true}, "model": {"enabled": true, "llm": true, "text_embedding": false, "rerank": false, "tts": false, "speech2text": false, "moderation": false}, "node": null, "endpoint": null, "storage": null}}, "plugins": {"tools": ["provider/wikipedia.yaml"], "models": null, "endpoints": null}, "tags": ["social"], "repo": null, "verified": true, "tool": {"identity": {"author": "lang<PERSON><PERSON>", "name": "wikipedia", "description": {"en_US": "Wikipedia is a free online encyclopedia, created and edited by volunteers around the world.", "zh_Hans": "维基百科是一个由全世界的志愿者创建和编辑的免费在线百科全书。", "pt_BR": "Wikipedia is a free online encyclopedia, created and edited by volunteers around the world.", "ja_JP": "Wikipedia is a free online encyclopedia, created and edited by volunteers around the world."}, "icon": "a83ac9eac258f6710e51050e914e9b97d978fcbfc2b53923f4f320c8eef44424.svg", "label": {"en_US": "Wikipedia", "zh_Hans": "维基百科", "pt_BR": "Wikipedia", "ja_JP": "Wikipedia"}, "tags": ["social"]}, "plugin_id": "langgenius/wikipedia", "credentials_schema": []}, "model": null, "endpoint": null, "agent_strategy": null, "meta": {"minimum_dify_version": null}}, "name": "wikipedia", "installation_id": "e2467a85-86a0-4027-aebd-cf0ed262e2b5"}, {"id": "caa16ebf-df86-4407-a562-392e39a0a8d8", "created_at": "2025-07-28T08:36:00.018738Z", "updated_at": "2025-07-28T08:36:00.018738Z", "tenant_id": "f24e8fa4-b522-48b2-a5ff-fe639b38d089", "endpoints_setups": 0, "endpoints_active": 0, "runtime_type": "local", "source": "marketplace", "meta": {"plugin_unique_identifier": "langgenius/yahoo:0.0.5@b0bf4aa3062c9b41ce3eaac8e4ea1987f7daef614702b4f3a11755629b35bb28"}, "plugin_id": "langgenius/yahoo", "plugin_unique_identifier": "langgenius/yahoo:0.0.5@b0bf4aa3062c9b41ce3eaac8e4ea1987f7daef614702b4f3a11755629b35bb28", "version": "0.0.5", "checksum": "b0bf4aa3062c9b41ce3eaac8e4ea1987f7daef614702b4f3a11755629b35bb28", "declaration": {"version": "0.0.5", "author": "lang<PERSON><PERSON>", "name": "yahoo", "description": {"en_US": "Finance, and Yahoo! get the latest news, stock quotes, and interactive chart with Yahoo!", "zh_Hans": "雅虎财经，获取并整理出最新的新闻、股票报价等一切你想要的财经信息。", "pt_BR": "Finance, and Yahoo! get the latest news, stock quotes, and interactive chart with Yahoo!", "ja_JP": "Finance, and Yahoo! get the latest news, stock quotes, and interactive chart with Yahoo!"}, "icon": "3bda2714e412b23883059aed5cfc5e882f357fd1ac563188b37eeeba400c39c9.png", "label": {"en_US": "YahooFinance", "zh_Hans": "雅虎财经", "pt_BR": "YahooFinance", "ja_JP": "YahooFinance"}, "category": "tool", "created_at": "2024-09-20T08:03:44.658609Z", "resource": {"memory": 1048576, "permission": {"tool": {"enabled": true}, "model": {"enabled": true, "llm": true, "text_embedding": false, "rerank": false, "tts": false, "speech2text": false, "moderation": false}, "node": null, "endpoint": null, "storage": null}}, "plugins": {"tools": ["provider/yahoo.yaml"], "models": null, "endpoints": null}, "tags": ["business", "finance"], "repo": null, "verified": true, "tool": {"identity": {"author": "lang<PERSON><PERSON>", "name": "yahoo", "description": {"en_US": "Finance, and Yahoo! get the latest news, stock quotes, and interactive chart with Yahoo!", "zh_Hans": "雅虎财经，获取并整理出最新的新闻、股票报价等一切你想要的财经信息。", "pt_BR": "Finance, and Yahoo! get the latest news, stock quotes, and interactive chart with Yahoo!", "ja_JP": "Finance, and Yahoo! get the latest news, stock quotes, and interactive chart with Yahoo!"}, "icon": "3bda2714e412b23883059aed5cfc5e882f357fd1ac563188b37eeeba400c39c9.png", "label": {"en_US": "YahooFinance", "zh_Hans": "雅虎财经", "pt_BR": "YahooFinance", "ja_JP": "YahooFinance"}, "tags": ["business", "finance"]}, "plugin_id": "langgenius/yahoo", "credentials_schema": []}, "model": null, "endpoint": null, "agent_strategy": null, "meta": {"minimum_dify_version": null}}, "name": "yahoo", "installation_id": "caa16ebf-df86-4407-a562-392e39a0a8d8"}, {"id": "308cd30c-5114-4f74-b219-4235946c27ba", "created_at": "2025-07-28T08:47:31.306648Z", "updated_at": "2025-07-28T08:47:31.306648Z", "tenant_id": "f24e8fa4-b522-48b2-a5ff-fe639b38d089", "endpoints_setups": 0, "endpoints_active": 0, "runtime_type": "local", "source": "marketplace", "meta": {"plugin_unique_identifier": "langgenius/searxng:0.0.7@fce43eac17ce659811cfa35bef8858d06f78bb832eff5d0317dab34982991eca"}, "plugin_id": "langgenius/searxng", "plugin_unique_identifier": "langgenius/searxng:0.0.7@fce43eac17ce659811cfa35bef8858d06f78bb832eff5d0317dab34982991eca", "version": "0.0.7", "checksum": "fce43eac17ce659811cfa35bef8858d06f78bb832eff5d0317dab34982991eca", "declaration": {"version": "0.0.7", "author": "lang<PERSON><PERSON>", "name": "searxng", "description": {"en_US": "A free internet metasearch engine.", "zh_Hans": "开源免费的互联网元搜索引擎", "pt_BR": "A free internet metasearch engine.", "ja_JP": "A free internet metasearch engine."}, "icon": "ed0f6d711dc2f632e3ead5e98389e06b78450e0675cb150216fa59650b191d3d.svg", "label": {"en_US": "SearXNG", "zh_Hans": "SearXNG", "pt_BR": "SearXNG", "ja_JP": "SearXNG"}, "category": "tool", "created_at": "2024-09-20T08:03:44.658609Z", "resource": {"memory": 1048576, "permission": {"tool": {"enabled": true}, "model": {"enabled": true, "llm": true, "text_embedding": false, "rerank": false, "tts": false, "speech2text": false, "moderation": false}, "node": null, "endpoint": null, "storage": null}}, "plugins": {"tools": ["provider/searxng.yaml"], "models": null, "endpoints": null}, "tags": ["search", "productivity"], "repo": null, "verified": true, "tool": {"identity": {"author": "Junytang", "name": "searxng", "description": {"en_US": "A free internet metasearch engine.", "zh_Hans": "开源免费的互联网元搜索引擎", "pt_BR": "A free internet metasearch engine.", "ja_JP": "A free internet metasearch engine."}, "icon": "ed0f6d711dc2f632e3ead5e98389e06b78450e0675cb150216fa59650b191d3d.svg", "label": {"en_US": "SearXNG", "zh_Hans": "SearXNG", "pt_BR": "SearXNG", "ja_JP": "SearXNG"}, "tags": ["search", "productivity"]}, "plugin_id": "langgenius/searxng", "credentials_schema": [{"type": "text-input", "name": "searxng_base_url", "scope": null, "required": true, "default": null, "options": null, "label": {"en_US": "SearXNG base URL", "zh_Hans": "SearXNG base URL", "pt_BR": "SearXNG base URL", "ja_JP": "SearXNG base URL"}, "help": null, "url": "https://docs.dify.ai/tutorials/tool-configuration/searxng", "placeholder": {"en_US": "Please input your SearXNG base URL", "zh_Hans": "请输入您的 SearXNG base URL", "pt_BR": "Please input your SearXNG base URL", "ja_JP": "Please input your SearXNG base URL"}}]}, "model": null, "endpoint": null, "agent_strategy": null, "meta": {"minimum_dify_version": null}}, "name": "searxng", "installation_id": "308cd30c-5114-4f74-b219-4235946c27ba"}, {"id": "49f2f479-497b-444d-b422-edab88b63791", "created_at": "2025-07-28T08:47:50.282474Z", "updated_at": "2025-07-28T08:47:50.282474Z", "tenant_id": "f24e8fa4-b522-48b2-a5ff-fe639b38d089", "endpoints_setups": 0, "endpoints_active": 0, "runtime_type": "local", "source": "marketplace", "meta": {"plugin_unique_identifier": "langgenius/regex:0.0.3@257eaab07b70ab1f77a881b870eefee93fc8fd0dd13350077410264f31695039"}, "plugin_id": "langgenius/regex", "plugin_unique_identifier": "langgenius/regex:0.0.3@257eaab07b70ab1f77a881b870eefee93fc8fd0dd13350077410264f31695039", "version": "0.0.3", "checksum": "257eaab07b70ab1f77a881b870eefee93fc8fd0dd13350077410264f31695039", "declaration": {"version": "0.0.3", "author": "lang<PERSON><PERSON>", "name": "regex", "description": {"en_US": "A tool for regex extraction.", "zh_Hans": "一个用于正则表达式内容提取的工具。", "pt_BR": "A tool for regex extraction.", "ja_JP": "A tool for regex extraction."}, "icon": "d436a70a8224c47ba88e07d7e5e85aacef607176b5d4004dd6237fc425e77098.svg", "label": {"en_US": "Regex", "zh_Hans": "正则表达式提取", "pt_BR": "Regex", "ja_JP": "Regex"}, "category": "tool", "created_at": "2024-09-20T08:03:44.658609Z", "resource": {"memory": 1048576, "permission": {"tool": {"enabled": true}, "model": {"enabled": true, "llm": true, "text_embedding": false, "rerank": false, "tts": false, "speech2text": false, "moderation": false}, "node": null, "endpoint": null, "storage": null}}, "plugins": {"tools": ["provider/regex.yaml"], "models": null, "endpoints": null}, "tags": ["utilities", "productivity"], "repo": null, "verified": true, "tool": {"identity": {"author": "<PERSON><PERSON><PERSON>", "name": "regex", "description": {"en_US": "A tool for regex extraction.", "zh_Hans": "一个用于正则表达式内容提取的工具。", "pt_BR": "A tool for regex extraction.", "ja_JP": "A tool for regex extraction."}, "icon": "d436a70a8224c47ba88e07d7e5e85aacef607176b5d4004dd6237fc425e77098.svg", "label": {"en_US": "Regex", "zh_Hans": "正则表达式提取", "pt_BR": "Regex", "ja_JP": "Regex"}, "tags": ["utilities", "productivity"]}, "plugin_id": "langgenius/regex", "credentials_schema": []}, "model": null, "endpoint": null, "agent_strategy": null, "meta": {"minimum_dify_version": null}}, "name": "regex", "installation_id": "49f2f479-497b-444d-b422-edab88b63791"}, {"id": "9344a7a7-7d4e-4922-9d07-ce90c2b3086c", "created_at": "2025-07-28T08:49:16.170987Z", "updated_at": "2025-07-28T08:49:16.170987Z", "tenant_id": "f24e8fa4-b522-48b2-a5ff-fe639b38d089", "endpoints_setups": 0, "endpoints_active": 0, "runtime_type": "local", "source": "marketplace", "meta": {"plugin_unique_identifier": "baobaobao/weather:0.0.3@18136b085ec7e22ad6739c0fdf4c92e1ae8d191566f8a3581f5a149aa8a6d28e"}, "plugin_id": "baobaobao/weather", "plugin_unique_identifier": "baobaobao/weather:0.0.3@18136b085ec7e22ad6739c0fdf4c92e1ae8d191566f8a3581f5a149aa8a6d28e", "version": "0.0.3", "checksum": "18136b085ec7e22ad6739c0fdf4c92e1ae8d191566f8a3581f5a149aa8a6d28e", "declaration": {"version": "0.0.3", "author": "baobaobao", "name": "weather", "description": {"en_US": "weather", "zh_Hans": "天气查询是一个用于获取实时天气数据和预报信息的服务接口。该API提供国内范围内的天气状况、预报和相关气象数据。", "pt_BR": "weather", "ja_JP": "weather"}, "icon": "09ee44c34c4c8cee99ed7347a5cf9cef032cf53251a362f088104f76f56ae51c.svg", "label": {"en_US": "weather", "zh_Hans": "天气查询", "pt_BR": "weather", "ja_JP": "weather"}, "category": "tool", "created_at": "2025-03-11T17:56:04.222910+08:00", "resource": {"memory": 268435456, "permission": {"tool": {"enabled": true}, "model": null, "node": null, "endpoint": {"enabled": true}, "storage": {"enabled": true, "size": 1048576}}}, "plugins": {"tools": ["provider/weather.yaml"], "models": null, "endpoints": null}, "tags": [], "repo": null, "verified": true, "tool": {"identity": {"author": "baobaobao", "name": "weather", "description": {"en_US": "weather", "zh_Hans": "天气查询是一个用于获取实时天气数据和预报信息的服务接口。该API提供国内范围内的天气状况、预报和相关气象数据。", "pt_BR": "weather", "ja_JP": "weather"}, "icon": "09ee44c34c4c8cee99ed7347a5cf9cef032cf53251a362f088104f76f56ae51c.svg", "label": {"en_US": "weather", "zh_Hans": "天气查询", "pt_BR": "weather", "ja_JP": "weather"}, "tags": []}, "plugin_id": "baobaobao/weather", "credentials_schema": [{"type": "secret-input", "name": "qweather_api_key", "scope": null, "required": true, "default": null, "options": null, "label": {"en_US": "QWeather API key", "zh_Hans": "QWeather API key", "pt_BR": "QWeather API key", "ja_JP": "QWeather API key"}, "help": {"en_US": "Get your QWeather API key from QWeather", "zh_Hans": "从 QWeather 获取您的 QWeather API key", "pt_BR": "Get your QWeather API key from QWeather", "ja_JP": "Get your QWeather API key from QWeather"}, "url": "https://dev.qweather.com/docs/start/", "placeholder": {"en_US": "Please input your QWeather API key", "zh_Hans": "请输入你的 QWeather API key", "pt_BR": "Please input your QWeather API key", "ja_JP": "Please input your QWeather API key"}}]}, "model": null, "endpoint": null, "agent_strategy": null, "meta": {"minimum_dify_version": null}}, "name": "weather", "installation_id": "9344a7a7-7d4e-4922-9d07-ce90c2b3086c"}, {"id": "c27c59f5-2cd6-4a0c-aaa1-fe5c703e3610", "created_at": "2025-07-28T09:05:23.260425Z", "updated_at": "2025-07-28T09:05:23.260425Z", "tenant_id": "f24e8fa4-b522-48b2-a5ff-fe639b38d089", "endpoints_setups": 0, "endpoints_active": 0, "runtime_type": "local", "source": "marketplace", "meta": {"plugin_unique_identifier": "langgenius/arxiv:0.0.2@906eec47ee2ae429a628ad0300173ad962d24930883814e36c1b0c2fc09bdfdf"}, "plugin_id": "langgenius/arxiv", "plugin_unique_identifier": "langgenius/arxiv:0.0.2@906eec47ee2ae429a628ad0300173ad962d24930883814e36c1b0c2fc09bdfdf", "version": "0.0.2", "checksum": "906eec47ee2ae429a628ad0300173ad962d24930883814e36c1b0c2fc09bdfdf", "declaration": {"version": "0.0.2", "author": "lang<PERSON><PERSON>", "name": "arxiv", "description": {"en_US": "ArXiv, https://arxiv.org.", "zh_Hans": "ArXiv，https://arxiv.org.", "pt_BR": "ArXiv, https://arxiv.org.", "ja_JP": "ArXiv, https://arxiv.org."}, "icon": "0d9a77329fb97c838ac700ed9065242a11b88c19edb4ba084205d320a3579bdb.svg", "label": {"en_US": "ArXiv", "zh_Hans": "ArXiv", "pt_BR": "ArXiv", "ja_JP": "ArXiv"}, "category": "tool", "created_at": "2024-09-20T08:03:44.658609Z", "resource": {"memory": 1048576, "permission": {"tool": {"enabled": true}, "model": {"enabled": true, "llm": true, "text_embedding": false, "rerank": false, "tts": false, "speech2text": false, "moderation": false}, "node": null, "endpoint": null, "storage": null}}, "plugins": {"tools": ["provider/arxiv.yaml"], "models": null, "endpoints": null}, "tags": ["search"], "repo": null, "verified": true, "tool": {"identity": {"author": "yash_parmar", "name": "arxiv", "description": {"en_US": "Access to a vast repository of scientific papers and articles in various fields of research.", "zh_Hans": "访问各个研究领域大量科学论文和文章的存储库。", "pt_BR": "Access to a vast repository of scientific papers and articles in various fields of research.", "ja_JP": "Access to a vast repository of scientific papers and articles in various fields of research."}, "icon": "0d9a77329fb97c838ac700ed9065242a11b88c19edb4ba084205d320a3579bdb.svg", "label": {"en_US": "ArXiv", "zh_Hans": "ArXiv", "pt_BR": "ArXiv", "ja_JP": "ArXiv"}, "tags": ["search"]}, "plugin_id": "langgenius/arxiv", "credentials_schema": []}, "model": null, "endpoint": null, "agent_strategy": null, "meta": {"minimum_dify_version": null}}, "name": "arxiv", "installation_id": "c27c59f5-2cd6-4a0c-aaa1-fe5c703e3610"}, {"id": "01a35533-418b-499b-9199-34bd38b34a70", "created_at": "2025-07-28T11:16:43.212826Z", "updated_at": "2025-07-28T11:16:43.212826Z", "tenant_id": "f24e8fa4-b522-48b2-a5ff-fe639b38d089", "endpoints_setups": 0, "endpoints_active": 0, "runtime_type": "local", "source": "marketplace", "meta": {"plugin_unique_identifier": "langgenius/e2b:0.0.1@62201c9d53574b721e5b0649cd4e73c57808b1e9484ad55b2f22c60def0edaba"}, "plugin_id": "langgenius/e2b", "plugin_unique_identifier": "langgenius/e2b:0.0.1@62201c9d53574b721e5b0649cd4e73c57808b1e9484ad55b2f22c60def0edaba", "version": "0.0.1", "checksum": "62201c9d53574b721e5b0649cd4e73c57808b1e9484ad55b2f22c60def0edaba", "declaration": {"version": "0.0.1", "author": "lang<PERSON><PERSON>", "name": "e2b", "description": {"en_US": "Tool for integration with e2b.dev.", "zh_Hans": "用于集成 e2b.dev 的工具插件。", "pt_BR": "Tool for integration with e2b.dev.", "ja_JP": "Tool for integration with e2b.dev."}, "icon": "972f9bc0261570a91b18fcdcfdf31d3e65b8db3c02a76ccf61f2a101282a0744.png", "label": {"en_US": "E2B", "zh_Hans": "E2B", "pt_BR": "E2B", "ja_JP": "E2B"}, "category": "tool", "created_at": "2025-02-10T16:17:18.683538+08:00", "resource": {"memory": 268435456, "permission": {"tool": null, "model": null, "node": null, "endpoint": null, "storage": null}}, "plugins": {"tools": ["provider/e2b.yaml"], "models": null, "endpoints": null}, "tags": [], "repo": null, "verified": true, "tool": {"identity": {"author": "lang<PERSON><PERSON>", "name": "e2b", "description": {"en_US": "Tool for integrate with e2b.dev.", "zh_Hans": "Tool for integrate with e2b.dev.", "pt_BR": "Tool for integrate with e2b.dev.", "ja_JP": "Tool for integrate with e2b.dev."}, "icon": "972f9bc0261570a91b18fcdcfdf31d3e65b8db3c02a76ccf61f2a101282a0744.png", "label": {"en_US": "E2B", "zh_Hans": "E2B", "pt_BR": "E2B", "ja_JP": "E2B"}, "tags": []}, "plugin_id": "langgenius/e2b", "credentials_schema": [{"type": "secret-input", "name": "api_key", "scope": null, "required": true, "default": null, "options": null, "label": {"en_US": "API Key", "zh_Hans": "API 密钥", "pt_BR": "API Key", "ja_JP": "API Key"}, "help": {"en_US": "You can get the API key from the E2B dashboard", "zh_Hans": "你可以从 E2B 仪表板获取 API 密钥", "pt_BR": "You can get the API key from the E2B dashboard", "ja_JP": "You can get the API key from the E2B dashboard"}, "url": "https://e2b.dev/dashboard?tab=keys", "placeholder": null}]}, "model": null, "endpoint": null, "agent_strategy": null, "meta": {"minimum_dify_version": null}}, "name": "e2b", "installation_id": "01a35533-418b-499b-9199-34bd38b34a70"}, {"id": "c4fce208-e53a-4c82-b76f-76062cb3f593", "created_at": "2025-07-28T11:19:13.725400Z", "updated_at": "2025-07-28T11:19:13.725400Z", "tenant_id": "f24e8fa4-b522-48b2-a5ff-fe639b38d089", "endpoints_setups": 0, "endpoints_active": 0, "runtime_type": "local", "source": "marketplace", "meta": {"plugin_unique_identifier": "langgenius/email:0.0.7@6c3856490275f3440619fabfdc54f3bd29bad8959e29996452277b9c3b0c5e9b"}, "plugin_id": "langgenius/email", "plugin_unique_identifier": "langgenius/email:0.0.7@6c3856490275f3440619fabfdc54f3bd29bad8959e29996452277b9c3b0c5e9b", "version": "0.0.7", "checksum": "6c3856490275f3440619fabfdc54f3bd29bad8959e29996452277b9c3b0c5e9b", "declaration": {"version": "0.0.7", "author": "lang<PERSON><PERSON>", "name": "email", "description": {"en_US": "send email through smtp protocol", "zh_Hans": "通过smtp协议发送电子邮件", "pt_BR": "send email through smtp protocol", "ja_JP": "send email through smtp protocol"}, "icon": "8f2d1b31d65a6085e8a845cb9bb7ec37399576001e6065fceaf344bffbeb393f.svg", "label": {"en_US": "email", "zh_Hans": "电子邮件", "pt_BR": "email", "ja_JP": "email"}, "category": "tool", "created_at": "2024-09-20T08:03:44.658609Z", "resource": {"memory": 1048576, "permission": {"tool": {"enabled": true}, "model": {"enabled": true, "llm": true, "text_embedding": false, "rerank": false, "tts": false, "speech2text": false, "moderation": false}, "node": null, "endpoint": null, "storage": null}}, "plugins": {"tools": ["provider/email.yaml"], "models": null, "endpoints": null}, "tags": ["utilities"], "repo": null, "verified": true, "tool": {"identity": {"author": "wakaka6", "name": "email", "description": {"en_US": "send email through smtp protocol", "zh_Hans": "通过smtp协议发送电子邮件", "pt_BR": "send email through smtp protocol", "ja_JP": "send email through smtp protocol"}, "icon": "8f2d1b31d65a6085e8a845cb9bb7ec37399576001e6065fceaf344bffbeb393f.svg", "label": {"en_US": "email", "zh_Hans": "电子邮件", "pt_BR": "email", "ja_JP": "email"}, "tags": ["utilities"]}, "plugin_id": "langgenius/email", "credentials_schema": [{"type": "text-input", "name": "email_account", "scope": null, "required": true, "default": null, "options": null, "label": {"en_US": "email account", "zh_Hans": "邮件账号", "pt_BR": "email account", "ja_JP": "email account"}, "help": {"en_US": "email account", "zh_Hans": "邮件账号", "pt_BR": "email account", "ja_JP": "email account"}, "url": null, "placeholder": {"en_US": "input you email account", "zh_Hans": "输入你的邮箱账号", "pt_BR": "input you email account", "ja_JP": "input you email account"}}, {"type": "secret-input", "name": "email_password", "scope": null, "required": true, "default": null, "options": null, "label": {"en_US": "email password", "zh_Hans": "邮件密码", "pt_BR": "email password", "ja_JP": "email password"}, "help": {"en_US": "email password", "zh_Hans": "邮件密码", "pt_BR": "email password", "ja_JP": "email password"}, "url": null, "placeholder": {"en_US": "email password", "zh_Hans": "邮件密码", "pt_BR": "email password", "ja_JP": "email password"}}, {"type": "select", "name": "encrypt_method", "scope": null, "required": true, "default": null, "options": [{"value": "NONE", "label": {"en_US": "NONE", "zh_Hans": "无加密", "pt_BR": "NONE", "ja_JP": "NONE"}}, {"value": "SSL", "label": {"en_US": "SSL", "zh_Hans": "SSL加密", "pt_BR": "SSL", "ja_JP": "SSL"}}, {"value": "TLS", "label": {"en_US": "START TLS", "zh_Hans": "START TLS加密", "pt_BR": "START TLS", "ja_JP": "START TLS"}}], "label": {"en_US": "encrypt method", "zh_Hans": "加密方式", "pt_BR": "encrypt method", "ja_JP": "encrypt method"}, "help": {"en_US": "smtp server encrypt method", "zh_Hans": "发信smtp服务器加密方式", "pt_BR": "smtp server encrypt method", "ja_JP": "smtp server encrypt method"}, "url": null, "placeholder": null}, {"type": "text-input", "name": "smtp_port", "scope": null, "required": true, "default": null, "options": null, "label": {"en_US": "smtp server port", "zh_Hans": "发信smtp服务器端口", "pt_BR": "smtp server port", "ja_JP": "smtp server port"}, "help": {"en_US": "smtp server port", "zh_Hans": "发信smtp服务器端口", "pt_BR": "smtp server port", "ja_JP": "smtp server port"}, "url": null, "placeholder": {"en_US": "smtp server port", "zh_Hans": "发信smtp服务器端口", "pt_BR": "smtp server port", "ja_JP": "smtp server port"}}, {"type": "text-input", "name": "smtp_server", "scope": null, "required": true, "default": null, "options": null, "label": {"en_US": "smtp server", "zh_Hans": "发信smtp服务器地址", "pt_BR": "smtp server", "ja_JP": "smtp server"}, "help": {"en_US": "smtp server", "zh_Hans": "发信smtp服务器地址", "pt_BR": "smtp server", "ja_JP": "smtp server"}, "url": null, "placeholder": {"en_US": "smtp server", "zh_Hans": "发信smtp服务器地址", "pt_BR": "smtp server", "ja_JP": "smtp server"}}, {"type": "text-input", "name": "sender_address", "scope": null, "required": false, "default": null, "options": null, "label": {"en_US": "Sender Address", "zh_Hans": "发件人地址", "pt_BR": "Sender Address", "ja_JP": "Sender Address"}, "help": {"en_US": "The designated sender address if different to the `email_account`. This may be required for using something like AWS's Simple EMail Service", "zh_Hans": "指定的发件人地址与 `email_account` 不同。使用类似 AWS 的 Simple EMail Service 时可能需要此设置。", "pt_BR": "The designated sender address if different to the `email_account`. This may be required for using something like AWS's Simple EMail Service", "ja_JP": "The designated sender address if different to the `email_account`. This may be required for using something like AWS's Simple EMail Service"}, "url": null, "placeholder": {"en_US": "sender address", "zh_Hans": "发件人地址", "pt_BR": "sender address", "ja_JP": "sender address"}}]}, "model": null, "endpoint": null, "agent_strategy": null, "meta": {"minimum_dify_version": null}}, "name": "email", "installation_id": "c4fce208-e53a-4c82-b76f-76062cb3f593"}, {"id": "a137dc97-a85e-44bc-a763-ddeafebeb1b3", "created_at": "2025-07-31T08:51:10.884713Z", "updated_at": "2025-07-31T08:51:10.884713Z", "tenant_id": "f24e8fa4-b522-48b2-a5ff-fe639b38d089", "endpoints_setups": 0, "endpoints_active": 0, "runtime_type": "local", "source": "package", "meta": {}, "plugin_id": "lang<PERSON>ius/dingtalk", "plugin_unique_identifier": "langgenius/dingtalk:0.0.4@1a167a005bd3509142f45f7a771f887c75437ea367840dee9760782a1a1ef082", "version": "0.0.4", "checksum": "1a167a005bd3509142f45f7a771f887c75437ea367840dee9760782a1a1ef082", "declaration": {"version": "0.0.4", "author": "lang<PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "description": {"en_US": "DingTalk group robot", "zh_Hans": "钉钉群机器人", "pt_BR": "DingTalk group robot", "ja_JP": "DingTalk group robot"}, "icon": "a21f02d16b0be15978b070528f938f57674668be2965c6604ed644b66e4d857a.svg", "label": {"en_US": "DingTalk", "zh_Hans": "钉钉", "pt_BR": "DingTalk", "ja_JP": "DingTalk"}, "category": "tool", "created_at": "2024-09-20T08:03:44.658609Z", "resource": {"memory": 1048576, "permission": {"tool": {"enabled": true}, "model": {"enabled": true, "llm": true, "text_embedding": false, "rerank": false, "tts": false, "speech2text": false, "moderation": false}, "node": null, "endpoint": null, "storage": null}}, "plugins": {"tools": ["provider/dingtalk.yaml"], "models": null, "endpoints": null}, "tags": ["social", "productivity"], "repo": null, "verified": true, "tool": {"identity": {"author": "Bowen Liang & LiuHao", "name": "<PERSON><PERSON><PERSON>", "description": {"en_US": "DingTalk group robot", "zh_Hans": "钉钉群机器人", "pt_BR": "DingTalk group robot", "ja_JP": "DingTalk group robot"}, "icon": "a21f02d16b0be15978b070528f938f57674668be2965c6604ed644b66e4d857a.svg", "label": {"en_US": "DingTalk", "zh_Hans": "钉钉", "pt_BR": "DingTalk", "ja_JP": "DingTalk"}, "tags": ["social", "productivity"]}, "plugin_id": "lang<PERSON>ius/dingtalk", "credentials_schema": []}, "model": null, "endpoint": null, "agent_strategy": null, "meta": {"minimum_dify_version": null}}, "name": "<PERSON><PERSON><PERSON>", "installation_id": "a137dc97-a85e-44bc-a763-ddeafebeb1b3"}, {"id": "a48a5c27-ea52-470d-890e-ac1c4c35f9fe", "created_at": "2025-07-31T08:51:17.142793Z", "updated_at": "2025-07-31T08:51:17.142793Z", "tenant_id": "f24e8fa4-b522-48b2-a5ff-fe639b38d089", "endpoints_setups": 0, "endpoints_active": 0, "runtime_type": "local", "source": "package", "meta": {}, "plugin_id": "langgenius/firecrawl", "plugin_unique_identifier": "langgenius/firecrawl:0.0.3@02f037a6e82c44103d3604456f8e57b5516464183344f32d74eec3f82d6036b0", "version": "0.0.3", "checksum": "02f037a6e82c44103d3604456f8e57b5516464183344f32d74eec3f82d6036b0", "declaration": {"version": "0.0.3", "author": "lang<PERSON><PERSON>", "name": "firecrawl", "description": {"en_US": "Firecrawl API integration for web crawling and scraping.", "zh_Hans": "Firecrawl API 集成，用于网页爬取和数据抓取。", "pt_BR": "Firecrawl API integration for web crawling and scraping.", "ja_JP": "Firecrawl API integration for web crawling and scraping."}, "icon": "48e81335e9c5fb6275f29e7a019e774ece91f1f63daf4b3d375f03accce99b62.svg", "label": {"en_US": "Firecrawl", "zh_Hans": "Firecrawl", "pt_BR": "Firecrawl", "ja_JP": "Firecrawl"}, "category": "tool", "created_at": "2024-09-20T08:03:44.658609Z", "resource": {"memory": 1048576, "permission": {"tool": {"enabled": true}, "model": {"enabled": true, "llm": true, "text_embedding": false, "rerank": false, "tts": false, "speech2text": false, "moderation": false}, "node": null, "endpoint": null, "storage": null}}, "plugins": {"tools": ["provider/firecrawl.yaml"], "models": null, "endpoints": null}, "tags": ["search", "utilities"], "repo": null, "verified": true, "tool": {"identity": {"author": "<PERSON><PERSON><PERSON>", "name": "firecrawl", "description": {"en_US": "Firecrawl API integration for web crawling and scraping.", "zh_Hans": "Firecrawl API 集成，用于网页爬取和数据抓取。", "pt_BR": "Firecrawl API integration for web crawling and scraping.", "ja_JP": "Firecrawl API integration for web crawling and scraping."}, "icon": "48e81335e9c5fb6275f29e7a019e774ece91f1f63daf4b3d375f03accce99b62.svg", "label": {"en_US": "Firecrawl", "zh_Hans": "Firecrawl", "pt_BR": "Firecrawl", "ja_JP": "Firecrawl"}, "tags": ["search", "utilities"]}, "plugin_id": "langgenius/firecrawl", "credentials_schema": [{"type": "text-input", "name": "base_url", "scope": null, "required": false, "default": null, "options": null, "label": {"en_US": "Firecrawl server's Base URL", "zh_Hans": "Firecrawl服务器的API URL", "pt_BR": "Firecrawl server's Base URL", "ja_JP": "Firecrawl server's Base URL"}, "help": null, "url": null, "placeholder": {"en_US": "https://api.firecrawl.dev", "zh_Hans": "https://api.firecrawl.dev", "pt_BR": "https://api.firecrawl.dev", "ja_JP": "https://api.firecrawl.dev"}}, {"type": "secret-input", "name": "firecrawl_api_key", "scope": null, "required": true, "default": null, "options": null, "label": {"en_US": "Firecrawl API Key", "zh_Hans": "Firecrawl API 密钥", "pt_BR": "Firecrawl API Key", "ja_JP": "Firecrawl API Key"}, "help": {"en_US": "Get your Firecrawl API key from your Firecrawl account settings.If you are using a self-hosted version, you may enter any key at your convenience.", "zh_Hans": "从您的 Firecrawl 账户设置中获取 Firecrawl API 密钥。如果是自托管版本，可以随意填写密钥。", "pt_BR": "Get your Firecrawl API key from your Firecrawl account settings.If you are using a self-hosted version, you may enter any key at your convenience.", "ja_JP": "Get your Firecrawl API key from your Firecrawl account settings.If you are using a self-hosted version, you may enter any key at your convenience."}, "url": "https://www.firecrawl.dev/account", "placeholder": {"en_US": "Please input your Firecrawl API key", "zh_Hans": "请输入您的 Firecrawl API 密钥，如果是自托管版本，可以随意填写密钥", "pt_BR": "Please input your Firecrawl API key", "ja_JP": "Please input your Firecrawl API key"}}]}, "model": null, "endpoint": null, "agent_strategy": null, "meta": {"minimum_dify_version": null}}, "name": "firecrawl", "installation_id": "a48a5c27-ea52-470d-890e-ac1c4c35f9fe"}, {"id": "8417a991-650b-4236-a5ce-1b9624addcb0", "created_at": "2025-08-01T03:45:48.616835Z", "updated_at": "2025-08-01T03:45:48.616835Z", "tenant_id": "f24e8fa4-b522-48b2-a5ff-fe639b38d089", "endpoints_setups": 0, "endpoints_active": 0, "runtime_type": "local", "source": "package", "meta": {}, "plugin_id": "langgenius/google", "plugin_unique_identifier": "langgenius/google:0.0.9@d360bbc433f39be1b11909cb9c32e6be4a17ea06af083f9e1c7613bb802bf517", "version": "0.0.9", "checksum": "d360bbc433f39be1b11909cb9c32e6be4a17ea06af083f9e1c7613bb802bf517", "declaration": {"version": "0.0.9", "author": "lang<PERSON><PERSON>", "name": "google", "description": {"en_US": "A tool for performing a Google SERP search and extracting snippets and webpages.Input should be a search query.", "zh_Hans": "一个用于执行 Google SERP 搜索并提取片段和网页的工具。输入应该是一个搜索查询。", "pt_BR": "A tool for performing a Google SERP search and extracting snippets and webpages.Input should be a search query.", "ja_JP": "A tool for performing a Google SERP search and extracting snippets and webpages.Input should be a search query."}, "icon": "1c5871163478957bac64c3fe33d72d003f767497d921c74b742aad27a8344a74.svg", "label": {"en_US": "Google", "zh_Hans": "Google", "pt_BR": "Google", "ja_JP": "Google"}, "category": "tool", "created_at": "2024-07-12T08:03:44.658609Z", "resource": {"memory": 1048576, "permission": {"tool": {"enabled": true}, "model": {"enabled": true, "llm": true, "text_embedding": false, "rerank": false, "tts": false, "speech2text": false, "moderation": false}, "node": null, "endpoint": null, "storage": null}}, "plugins": {"tools": ["provider/google.yaml"], "models": null, "endpoints": null}, "tags": ["search"], "repo": null, "verified": true, "tool": {"identity": {"author": "Dify", "name": "google", "description": {"en_US": "Google", "zh_Hans": "GoogleSearch", "pt_BR": "Google", "ja_JP": "Google"}, "icon": "1c5871163478957bac64c3fe33d72d003f767497d921c74b742aad27a8344a74.svg", "label": {"en_US": "Google", "zh_Hans": "Google", "pt_BR": "Google", "ja_JP": "Google"}, "tags": ["search"]}, "plugin_id": "langgenius/google", "credentials_schema": [{"type": "secret-input", "name": "serpapi_api_key", "scope": null, "required": true, "default": null, "options": null, "label": {"en_US": "SerpApi API key", "zh_Hans": "SerpApi API key", "pt_BR": "SerpApi API key", "ja_JP": "SerpApi API key"}, "help": {"en_US": "Get your SerpApi API key from SerpApi", "zh_Hans": "从 SerpApi 获取您的 SerpApi API key", "pt_BR": "Get your SerpApi API key from SerpApi", "ja_JP": "Get your SerpApi API key from SerpApi"}, "url": "https://serpapi.com/manage-api-key", "placeholder": {"en_US": "Please input your SerpApi API key", "zh_Hans": "请输入你的 SerpApi API key", "pt_BR": "Please input your SerpApi API key", "ja_JP": "Please input your SerpApi API key"}}]}, "model": null, "endpoint": null, "agent_strategy": null, "meta": {"minimum_dify_version": null}}, "name": "google", "installation_id": "8417a991-650b-4236-a5ce-1b9624addcb0"}]}