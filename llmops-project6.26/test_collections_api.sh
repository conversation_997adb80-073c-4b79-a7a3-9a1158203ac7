#!/bin/bash

echo "🚀 测试工具集合API..."
echo "=================================="

# API配置
API_URL="http://172.16.201.93:30080/api/v1/tool/collections"
AUTH_TOKEN="Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5MDk5OTEsImlhdCI6MTY1NjMwOTk5MSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.G9bUZ984g-8M-yBuUisrW9HtIK-yP0G-tD4DW9VQ7xAvmyMRaTOJ9DlucZj7KK_ynES1Qa96HZV_Wz5KlUUWtA"

echo "📡 调用API: $API_URL"
echo "🔑 使用认证: ${AUTH_TOKEN:0:50}..."
echo ""

# 调用API
response=$(curl -s -w "\n%{http_code}" \
  --location \
  --request GET "$API_URL" \
  --header 'Content-Type: application/json' \
  --header "Authorization: $AUTH_TOKEN")

# 分离响应体和状态码
http_code=$(echo "$response" | tail -n1)
response_body=$(echo "$response" | head -n -1)

echo "📊 HTTP状态码: $http_code"
echo ""

if [ "$http_code" = "200" ]; then
    echo "✅ API调用成功!"
    echo ""
    
    # 解析JSON响应
    echo "📋 响应数据:"
    echo "$response_body" | python3 -m json.tool 2>/dev/null || echo "$response_body"
    echo ""
    
    # 统计不同类型的工具
    echo "📊 工具类型统计:"
    
    # 提取server_type字段并统计
    rest_count=$(echo "$response_body" | grep -o '"server_type":"rest"' | wc -l)
    mcp_count=$(echo "$response_body" | grep -o '"server_type":"mcp"' | wc -l)
    dmcp_count=$(echo "$response_body" | grep -o '"server_type":"dynamic_mcp"' | wc -l)
    dify_count=$(echo "$response_body" | grep -o '"server_type":"dify"' | wc -l)
    
    echo "  🔧 REST类型: $rest_count 个"
    echo "  🤖 MCP类型: $mcp_count 个"
    echo "  ⚡ Dynamic MCP类型: $dmcp_count 个"
    echo "  🎯 Dify类型: $dify_count 个"
    
    total_count=$((rest_count + mcp_count + dmcp_count + dify_count))
    echo "  📈 总计: $total_count 个工具"
    
    if [ "$dify_count" -gt 0 ]; then
        echo ""
        echo "🎉 Dify插件集成成功!"
        echo "✅ 发现 $dify_count 个Dify类型的工具"
    else
        echo ""
        echo "⚠️  没有发现Dify类型的工具"
        echo "💡 可能的原因:"
        echo "   1. agent-tool-api服务未启动"
        echo "   2. Dify插件API返回空数据"
        echo "   3. 网络连接问题"
    fi
    
else
    echo "❌ API调用失败!"
    echo "📄 响应内容:"
    echo "$response_body"
    echo ""
    echo "💡 可能的原因:"
    echo "   1. 服务未启动或不可访问"
    echo "   2. 认证token无效"
    echo "   3. API路径错误"
fi

echo ""
echo "=================================="
echo "测试完成"
