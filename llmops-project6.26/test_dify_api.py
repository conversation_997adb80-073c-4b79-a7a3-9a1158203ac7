#!/usr/bin/env python3
"""
测试Dify插件集成的API接口
"""

import requests
import json
import sys

def test_agent_tool_api():
    """测试agent-tool-api的Dify插件接口"""
    print("🔍 测试 agent-tool-api 的 Dify 插件接口...")
    
    url = "http://localhost:8080/v1/dify-plugins/installed"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ agent-tool-api 响应成功!")
            print(f"成功: {data.get('success', False)}")
            print(f"消息: {data.get('message', 'N/A')}")
            
            plugins = data.get('data', [])
            print(f"插件数量: {len(plugins)}")
            
            if plugins:
                print("\n📋 插件列表:")
                for i, plugin in enumerate(plugins[:3], 1):  # 只显示前3个
                    print(f"  {i}. {plugin.get('plugin_name', 'N/A')}")
                    print(f"     描述: {plugin.get('plugin_description', 'N/A')[:50]}...")
                    print(f"     版本: {plugin.get('plugin_version', 'N/A')}")
                    print(f"     授权状态: {plugin.get('auth_status', 'N/A')}")
                    print()
            
            return True, data
        else:
            print(f"❌ agent-tool-api 响应失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False, None
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到 agent-tool-api (localhost:8080)")
        print("请确保 agent-tool-api 服务正在运行")
        return False, None
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False, None

def test_applet_backend_api():
    """测试applet-backend的工具集合接口"""
    print("\n🔍 测试 applet-backend 的工具集合接口...")
    
    url = "http://localhost:9090/api/v1/app/collections"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ applet-backend 响应成功!")
            
            collections = data.get('data', [])
            print(f"工具集合数量: {len(collections)}")
            
            # 按类型分类统计
            type_counts = {}
            dify_tools = []
            
            for collection in collections:
                server_type = collection.get('server_type', 'unknown')
                type_counts[server_type] = type_counts.get(server_type, 0) + 1
                
                if server_type == 'dify':
                    dify_tools.append(collection)
            
            print("\n📊 工具类型统计:")
            for tool_type, count in type_counts.items():
                print(f"  {tool_type}: {count} 个")
            
            if dify_tools:
                print(f"\n🎯 Dify 插件 ({len(dify_tools)} 个):")
                for i, tool in enumerate(dify_tools, 1):
                    print(f"  {i}. {tool.get('name', 'N/A')}")
                    print(f"     ID: {tool.get('id', 'N/A')}")
                    print(f"     描述: {tool.get('desc', 'N/A')[:50]}...")
                    print(f"     类型: {tool.get('server_type', 'N/A')}")
                    print()
            else:
                print("\n⚠️  没有找到 Dify 类型的工具")
            
            return True, data
        else:
            print(f"❌ applet-backend 响应失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False, None
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到 applet-backend (localhost:9090)")
        print("请确保 applet-backend 服务正在运行")
        return False, None
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False, None

def test_direct_dify_integration():
    """直接测试Dify插件集成逻辑"""
    print("\n🔍 测试 Dify 插件集成逻辑...")
    
    # 模拟agent-tool-api的响应
    mock_dify_response = {
        "success": True,
        "data": [
            {
                "provider_name": "siliconflow",
                "plugin_unique_identifier": "langgenius/siliconflow:0.0.20@a0297ff9ba92d57b12efa51dad87bbf68f6556979d2f32ed15fc833a3a1f4f39",
                "plugin_name": "siliconflow",
                "plugin_description": "硅基流动提供对各种模型（LLM、文本嵌入、重排序、STT、TTS）的访问，可通过模型名称、API密钥和其他参数进行配置。",
                "plugin_version": "0.0.20",
                "auth_status": "no_auth_required"
            },
            {
                "provider_name": "wecom",
                "plugin_unique_identifier": "langgenius/wecom:0.0.3@7a90abb01f25de45d35f26b6bd57dd8d70e0e2c74703274df43d6b2f1c648603",
                "plugin_name": "企业微信",
                "plugin_description": "企业微信群机器人",
                "plugin_version": "0.0.3",
                "auth_status": "no_auth_required"
            }
        ]
    }
    
    print("📋 模拟的 Dify 插件数据:")
    for plugin in mock_dify_response['data']:
        print(f"  - {plugin['plugin_name']} (v{plugin['plugin_version']})")
    
    # 模拟转换为工具集合格式
    print("\n🔄 转换为工具集合格式:")
    converted_tools = []
    
    for plugin in mock_dify_response['data']:
        tool = {
            "id": f"dify-plugin-{plugin['provider_name']}",
            "name": plugin['plugin_name'],
            "desc": plugin['plugin_description'],
            "api_tool_cnt": 1,
            "released": True,
            "server_type": "dify",
            "type": "builtin"
        }
        converted_tools.append(tool)
        
        print(f"  ✅ {tool['name']}")
        print(f"     ID: {tool['id']}")
        print(f"     类型: {tool['server_type']}")
        print()
    
    return converted_tools

def main():
    """主测试函数"""
    print("🚀 开始测试 Dify 插件集成...")
    print("=" * 60)
    
    # 测试1: agent-tool-api
    agent_success, agent_data = test_agent_tool_api()
    
    # 测试2: applet-backend
    backend_success, backend_data = test_applet_backend_api()
    
    # 测试3: 集成逻辑
    converted_tools = test_direct_dify_integration()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"  agent-tool-api: {'✅ 成功' if agent_success else '❌ 失败'}")
    print(f"  applet-backend: {'✅ 成功' if backend_success else '❌ 失败'}")
    print(f"  集成逻辑: ✅ 正常")
    
    if not agent_success:
        print("\n💡 建议:")
        print("  1. 启动 agent-tool-api 服务: cd agent-tool-api && python3 app.py")
        print("  2. 确保端口 8080 可用")
    
    if not backend_success:
        print("\n💡 建议:")
        print("  1. 启动 applet-backend 服务")
        print("  2. 确保端口 9090 可用")
        print("  3. 检查服务配置")

if __name__ == "__main__":
    main()
