#!/usr/bin/env python3
"""
测试Dify插件功能修复
"""

import requests
import json
import sys

def test_plugin_apis():
    """测试插件API修复效果"""
    
    # 配置
    base_url = "http://172.16.201.93:30080"
    auth_token = "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5MDk5OTEsImlhdCI6MTY1NjMwOTk5MSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.G9bUZ984g-8M-yBuUisrW9HtIK-yP0G-tD4DW9VQ7xAvmyMRaTOJ9DlucZj7KK_ynES1Qa96HZV_Wz5KlUUWtA"
    
    headers = {
        'Authorization': auth_token,
        'Content-Type': 'application/json'
    }
    
    print("🧪 测试Dify插件功能修复")
    print("=" * 60)
    
    # 测试1: 获取插件列表
    print("\n📋 测试1: 获取插件列表")
    try:
        response = requests.get(f"{base_url}/api/v1/app/dify-plugins/plugins", headers=headers, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            plugins = data.get('plugins', [])
            print(f"✅ 成功获取插件列表，共 {len(plugins)} 个插件")
            
            if plugins:
                print("插件列表:")
                for i, plugin in enumerate(plugins[:3]):  # 只显示前3个
                    print(f"  {i+1}. {plugin.get('name', 'Unknown')} ({plugin.get('plugin_id', 'Unknown')})")
                    print(f"     版本: {plugin.get('version', 'Unknown')}")
                    print(f"     状态: {plugin.get('status', 'Unknown')}")
                    print(f"     来源: {plugin.get('source', 'Unknown')}")
                if len(plugins) > 3:
                    print(f"  ... 还有 {len(plugins) - 3} 个插件")
            else:
                print("⚠️  插件列表为空")
        else:
            print(f"❌ 获取插件列表失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 测试2: 获取插件详情（如果有插件的话）
    print("\n📄 测试2: 获取插件详情")
    try:
        # 先获取插件列表
        response = requests.get(f"{base_url}/api/v1/app/dify-plugins/plugins", headers=headers, timeout=30)
        if response.status_code == 200:
            data = response.json()
            plugins = data.get('plugins', [])
            
            if plugins:
                # 测试第一个插件的详情
                test_plugin = plugins[0]
                plugin_id = test_plugin.get('plugin_id')
                
                print(f"测试插件: {plugin_id}")
                
                detail_response = requests.get(
                    f"{base_url}/api/v1/app/dify-plugins/plugins/{plugin_id}", 
                    headers=headers, 
                    timeout=30
                )
                
                print(f"状态码: {detail_response.status_code}")
                
                if detail_response.status_code == 200:
                    detail_data = detail_response.json()
                    print("✅ 成功获取插件详情")
                    print(f"  名称: {detail_data.get('name', 'Unknown')}")
                    print(f"  版本: {detail_data.get('version', 'Unknown')}")
                    print(f"  描述: {detail_data.get('description', 'No description')[:100]}...")
                    print(f"  工具数量: {len(detail_data.get('tools', []))}")
                else:
                    print(f"❌ 获取插件详情失败: {detail_response.text}")
            else:
                print("⚠️  没有插件可测试")
        else:
            print("⚠️  无法获取插件列表进行测试")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 测试3: 获取授权表单（如果有插件的话）
    print("\n🔐 测试3: 获取授权表单")
    try:
        # 先获取插件列表
        response = requests.get(f"{base_url}/api/v1/app/dify-plugins/plugins", headers=headers, timeout=30)
        if response.status_code == 200:
            data = response.json()
            plugins = data.get('plugins', [])
            
            if plugins:
                # 测试第一个插件的授权表单
                test_plugin = plugins[0]
                plugin_id = test_plugin.get('plugin_id')
                
                print(f"测试插件: {plugin_id}")
                
                auth_response = requests.get(
                    f"{base_url}/api/v1/app/dify-plugins/plugins/{plugin_id}/auth/form", 
                    headers=headers, 
                    timeout=30
                )
                
                print(f"状态码: {auth_response.status_code}")
                
                if auth_response.status_code == 200:
                    auth_data = auth_response.json()
                    print("✅ 成功获取授权表单")
                    fields = auth_data.get('fields', [])
                    print(f"  授权字段数量: {len(fields)}")
                    for field in fields[:3]:  # 只显示前3个字段
                        print(f"    - {field.get('name', 'Unknown')}: {field.get('label', 'No label')}")
                else:
                    print(f"⚠️  获取授权表单失败: {auth_response.text}")
            else:
                print("⚠️  没有插件可测试")
        else:
            print("⚠️  无法获取插件列表进行测试")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 测试4: 获取工具列表
    print("\n🔧 测试4: 获取工具列表")
    try:
        response = requests.get(f"{base_url}/api/v1/app/dify-plugins/plugins/tools", headers=headers, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            tools = data.get('tools', [])
            print(f"✅ 成功获取工具列表，共 {len(tools)} 个工具")
            
            if tools:
                print("工具列表:")
                for i, tool in enumerate(tools[:3]):  # 只显示前3个
                    print(f"  {i+1}. {tool.get('name', 'Unknown')} ({tool.get('key', 'Unknown')})")
                    print(f"     描述: {tool.get('description', 'No description')[:50]}...")
                if len(tools) > 3:
                    print(f"  ... 还有 {len(tools) - 3} 个工具")
            else:
                print("⚠️  工具列表为空")
        else:
            print(f"❌ 获取工具列表失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    print("\n" + "=" * 60)
    print("📊 修复内容总结:")
    print("✅ 修复了插件列表API - 现在从Dify服务器获取所有已安装插件")
    print("✅ 修复了插件详情API - 支持获取marketplace插件信息")
    print("✅ 修复了插件卸载API - 使用正确的installation_id")
    print("✅ 修复了API路径 - 使用/api/v1/app前缀")
    
    print("\n🎯 预期效果:")
    print("- 插件列表应该显示Dify中所有已安装的插件（包括marketplace插件）")
    print("- 插件详情应该能获取任何已安装插件的信息")
    print("- 插件卸载应该能正确删除Dify服务器上的插件")
    print("- 不再出现'plugin already installed'错误")

def main():
    """主函数"""
    print("开始测试Dify插件功能修复...")
    
    test_plugin_apis()
    
    print(f"\n💡 提示:")
    print("如果仍然遇到问题，请检查:")
    print("1. agent-tool-api服务是否正在运行")
    print("2. Dify服务器是否可访问")
    print("3. 认证令牌是否有效")
    print("4. 网络连接是否正常")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
