#!/usr/bin/env python3
"""
测试授权字段逻辑
验证不同授权状态下auth_fields字段的返回情况
"""

import requests
import json

def test_auth_fields_logic():
    """测试授权字段逻辑"""
    print("🔍 测试授权字段逻辑...")
    
    try:
        url = "http://localhost:8080/v1/dify-plugins/installed"
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                plugins = data.get('data', [])
                print(f"✅ 获取到 {len(plugins)} 个插件")
                
                # 按授权状态分类统计
                auth_status_stats = {
                    'no_auth_required': [],
                    'authorized': [],
                    'unauthorized': []
                }
                
                for plugin in plugins:
                    auth_status = plugin.get('auth_status', 'unknown')
                    plugin_name = plugin.get('plugin_name', 'Unknown')
                    has_auth_fields = 'auth_fields' in plugin
                    auth_fields_count = len(plugin.get('auth_fields', []))
                    
                    if auth_status in auth_status_stats:
                        auth_status_stats[auth_status].append({
                            'name': plugin_name,
                            'has_auth_fields': has_auth_fields,
                            'auth_fields_count': auth_fields_count
                        })
                
                # 输出统计结果
                print(f"\n📊 授权状态统计:")
                
                for status, plugins_list in auth_status_stats.items():
                    print(f"\n🔸 {status} ({len(plugins_list)} 个插件):")
                    
                    for plugin_info in plugins_list:
                        name = plugin_info['name']
                        has_fields = plugin_info['has_auth_fields']
                        fields_count = plugin_info['auth_fields_count']
                        
                        if status == 'no_auth_required':
                            if has_fields:
                                print(f"  ❌ {name}: 不应该有auth_fields字段，但有 {fields_count} 个")
                            else:
                                print(f"  ✅ {name}: 正确，无auth_fields字段")
                        else:
                            if has_fields:
                                print(f"  ✅ {name}: 正确，有 {fields_count} 个auth_fields")
                            else:
                                print(f"  ❌ {name}: 应该有auth_fields字段，但没有")
                
                # 验证逻辑正确性
                print(f"\n🎯 逻辑验证:")
                errors = 0
                
                for plugin in plugins:
                    auth_status = plugin.get('auth_status', 'unknown')
                    has_auth_fields = 'auth_fields' in plugin
                    plugin_name = plugin.get('plugin_name', 'Unknown')
                    
                    if auth_status == 'no_auth_required' and has_auth_fields:
                        print(f"  ❌ 错误: {plugin_name} 状态为 no_auth_required 但有 auth_fields")
                        errors += 1
                    elif auth_status in ['authorized', 'unauthorized'] and not has_auth_fields:
                        print(f"  ❌ 错误: {plugin_name} 状态为 {auth_status} 但没有 auth_fields")
                        errors += 1
                
                if errors == 0:
                    print("  ✅ 所有插件的auth_fields逻辑都正确！")
                else:
                    print(f"  ❌ 发现 {errors} 个逻辑错误")
                
                # 显示一些示例
                print(f"\n📋 示例插件:")
                for status in ['no_auth_required', 'authorized', 'unauthorized']:
                    status_plugins = [p for p in plugins if p.get('auth_status') == status]
                    if status_plugins:
                        example = status_plugins[0]
                        print(f"\n🔸 {status} 示例:")
                        print(f"  插件名: {example.get('plugin_name', 'Unknown')}")
                        print(f"  提供者: {example.get('provider_name', 'Unknown')}")
                        
                        if 'auth_fields' in example:
                            auth_fields = example['auth_fields']
                            print(f"  授权字段 ({len(auth_fields)} 个):")
                            for field in auth_fields:
                                print(f"    - {field.get('name', 'N/A')}: {field.get('label', 'N/A')} (必填: {field.get('required', False)})")
                        else:
                            print(f"  授权字段: 无")
                
            else:
                print(f"❌ API返回失败: {data.get('message', 'Unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def main():
    """主函数"""
    print("🚀 开始测试授权字段逻辑")
    print("=" * 60)
    
    test_auth_fields_logic()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成")

if __name__ == "__main__":
    main()
