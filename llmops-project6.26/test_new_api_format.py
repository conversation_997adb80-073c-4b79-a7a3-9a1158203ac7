#!/usr/bin/env python3
"""
测试新的API响应格式
"""

import requests
import json

def test_new_api_format():
    """测试新的API响应格式"""
    print("🔍 测试新的API响应格式...")
    
    # 测试agent-tool-api
    print("\n📋 测试agent-tool-api...")
    try:
        url = "http://localhost:8080/v1/dify-plugins/installed"
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ agent-tool-api响应成功")
            
            if data.get('success'):
                plugins = data.get('data', [])
                print(f"获取到 {len(plugins)} 个插件")
                
                # 检查第一个插件的字段
                if plugins:
                    first_plugin = plugins[0]
                    print(f"\n📊 第一个插件的字段:")
                    for key, value in first_plugin.items():
                        if key == 'auth_fields':
                            print(f"  {key}: {len(value)} 个授权字段")
                            for field in value:
                                print(f"    - {field.get('name', 'N/A')}: {field.get('label', 'N/A')} (必填: {field.get('required', False)})")
                        else:
                            print(f"  {key}: {value}")
                
                # 统计授权状态
                auth_stats = {}
                for plugin in plugins:
                    auth_status = plugin.get('auth_status', 'unknown')
                    auth_stats[auth_status] = auth_stats.get(auth_status, 0) + 1
                
                print(f"\n📈 授权状态统计:")
                for status, count in auth_stats.items():
                    print(f"  {status}: {count}")
                
            else:
                print(f"❌ API返回失败: {data.get('message', 'Unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 测试applet-backend
    print("\n🔧 测试applet-backend...")
    try:
        url = "http://172.16.201.93:30080/api/v1/app/dify-plugins/installed"
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5MDk5OTEsImlhdCI6MTY1NjMwOTk5MSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.G9bUZ984g-8M-yBuUisrW9HtIK-yP0G-tD4DW9VQ7xAvmyMRaTOJ9DlucZj7KK_ynES1Qa96HZV_Wz5KlUUWtA"
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ applet-backend响应成功")
            
            if isinstance(data, list):
                plugins = data
                print(f"获取到 {len(plugins)} 个插件")
                
                # 检查第一个插件的字段
                if plugins:
                    first_plugin = plugins[0]
                    print(f"\n📊 第一个插件的字段:")
                    for key, value in first_plugin.items():
                        if key == 'auth_fields':
                            print(f"  {key}: {len(value)} 个授权字段")
                            for field in value:
                                print(f"    - {field.get('name', 'N/A')}: {field.get('label', 'N/A')} (必填: {field.get('required', False)})")
                        else:
                            print(f"  {key}: {value}")
                
                # 验证字段完整性
                expected_fields = ['provider_name', 'plugin_unique_identifier', 'plugin_name', 
                                 'plugin_description', 'plugin_version', 'auth_status', 'auth_fields']
                
                print(f"\n🔍 字段完整性检查:")
                for plugin in plugins[:3]:  # 检查前3个插件
                    plugin_name = plugin.get('plugin_name', 'Unknown')
                    missing_fields = []
                    for field in expected_fields:
                        if field not in plugin:
                            missing_fields.append(field)
                    
                    if missing_fields:
                        print(f"  ❌ {plugin_name}: 缺少字段 {missing_fields}")
                    else:
                        print(f"  ✅ {plugin_name}: 所有字段完整")
                
            else:
                print(f"❌ 响应格式错误，期望数组，得到: {type(data)}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def main():
    """主函数"""
    print("🚀 开始测试新的API响应格式")
    print("=" * 60)
    
    test_new_api_format()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成")

if __name__ == "__main__":
    main()
