#!/usr/bin/env python3
"""
测试授权表单修复效果
"""

import requests
import json
import sys
import urllib.parse

def test_auth_form_fixes():
    """测试授权表单修复效果"""
    
    # 配置
    base_url = "http://172.16.201.93:30080"
    auth_token = "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5MDk5OTEsImlhdCI6MTY1NjMwOTk5MSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.G9bUZ984g-8M-yBuUisrW9HtIK-yP0G-tD4DW9VQ7xAvmyMRaTOJ9DlucZj7KK_ynES1Qa96HZV_Wz5KlUUWtA"
    
    headers = {
        'Authorization': auth_token,
        'Content-Type': 'application/json'
    }
    
    print("🔧 测试授权表单修复效果")
    print("=" * 60)
    
    # 1. 测试删除插件的错误处理
    print("\n🗑️  测试1: 删除插件的错误处理")
    
    deleted_plugin_ids = ['google', 'nonexistent_plugin', 'deleted_test']
    
    for plugin_id in deleted_plugin_ids:
        print(f"\n测试删除的插件: '{plugin_id}'")
        
        try:
            auth_response = requests.get(
                f"{base_url}/api/v1/app/dify-plugins/plugins/{plugin_id}/auth/form", 
                headers=headers, 
                timeout=15
            )
            
            print(f"  状态码: {auth_response.status_code}")
            
            if auth_response.status_code == 404:
                response_data = auth_response.json()
                print(f"  ✅ 正确返回404错误")
                print(f"  错误信息: {response_data.get('msg', 'No message')}")
                
                # 检查是否包含"插件不存在"
                if '插件不存在' in response_data.get('msg', ''):
                    print(f"  ✅ 错误信息正确包含'插件不存在'")
                else:
                    print(f"  ⚠️  错误信息不包含'插件不存在'")
                    
            elif auth_response.status_code == 200:
                response_data = auth_response.json()
                print(f"  ❌ 不应该返回200，响应: {response_data}")
            else:
                print(f"  ⚠️  其他状态码: {auth_response.text[:100]}...")
                
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")
    
    # 2. 测试存在插件的授权表单
    print(f"\n📋 测试2: 存在插件的授权表单")
    
    # 先获取插件列表
    try:
        response = requests.get(f"{base_url}/api/v1/app/dify-plugins/plugins", headers=headers, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            plugins = data.get('plugins', [])
            
            if plugins:
                # 测试前几个插件
                for i, plugin in enumerate(plugins[:3]):
                    plugin_id = plugin.get('plugin_id', 'Unknown')
                    name = plugin.get('name', 'Unknown')
                    
                    print(f"\n测试插件: {name} ({plugin_id})")
                    
                    try:
                        encoded_id = urllib.parse.quote(plugin_id, safe='')
                        auth_response = requests.get(
                            f"{base_url}/api/v1/app/dify-plugins/plugins/{encoded_id}/auth/form", 
                            headers=headers, 
                            timeout=15
                        )
                        
                        print(f"  状态码: {auth_response.status_code}")
                        
                        if auth_response.status_code == 200:
                            auth_data = auth_response.json()
                            fields = auth_data.get('fields', [])
                            print(f"  ✅ 成功获取授权表单，字段数量: {len(fields)}")
                            
                            if fields:
                                print("  授权字段:")
                                for field in fields[:3]:  # 只显示前3个字段
                                    print(f"    - {field.get('name', 'Unknown')}: {field.get('label', 'No label')}")
                                    print(f"      类型: {field.get('type', 'Unknown')}, 必需: {field.get('required', False)}")
                            else:
                                print("  ⚠️  插件不需要授权（无字段）")
                                
                        elif auth_response.status_code == 404:
                            print(f"  ❌ 插件不存在（但在列表中存在）")
                        else:
                            print(f"  ❌ 其他错误: {auth_response.text[:100]}...")
                            
                    except Exception as e:
                        print(f"  ❌ 请求异常: {e}")
            else:
                print("⚠️  插件列表为空，无法测试")
        else:
            print(f"❌ 获取插件列表失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 获取插件列表异常: {e}")
    
    # 3. 测试特定的firecrawl插件
    print(f"\n🔥 测试3: firecrawl插件授权表单")
    
    firecrawl_ids = ['firecrawl', 'langgenius/firecrawl', 'Firecrawl']
    
    for test_id in firecrawl_ids:
        print(f"\n测试firecrawl ID: '{test_id}'")
        
        try:
            # 先测试插件详情
            encoded_id = urllib.parse.quote(test_id, safe='')
            detail_response = requests.get(
                f"{base_url}/api/v1/app/dify-plugins/plugins/{encoded_id}", 
                headers=headers, 
                timeout=15
            )
            
            print(f"  插件详情状态码: {detail_response.status_code}")
            
            if detail_response.status_code == 200:
                detail_data = detail_response.json()
                print(f"  ✅ 插件存在: {detail_data.get('name', 'Unknown')}")
                
                # 测试授权表单
                auth_response = requests.get(
                    f"{base_url}/api/v1/app/dify-plugins/plugins/{encoded_id}/auth/form", 
                    headers=headers, 
                    timeout=15
                )
                
                print(f"  授权表单状态码: {auth_response.status_code}")
                
                if auth_response.status_code == 200:
                    auth_data = auth_response.json()
                    fields = auth_data.get('fields', [])
                    print(f"  ✅ 成功获取授权表单，字段数量: {len(fields)}")
                    
                    if fields:
                        print("  授权字段:")
                        for field in fields:
                            print(f"    - {field.get('name', 'Unknown')}: {field.get('label', 'No label')}")
                    else:
                        print("  ⚠️  插件不需要授权")
                        
                elif auth_response.status_code == 404:
                    print(f"  ❌ 授权表单返回404（但插件存在）")
                else:
                    print(f"  ❌ 授权表单其他错误: {auth_response.text[:100]}...")
                    
            elif detail_response.status_code == 404:
                print(f"  ❌ 插件不存在")
            else:
                print(f"  ❌ 插件详情其他错误: {detail_response.text[:100]}...")
                
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")
    
    print(f"\n" + "=" * 60)
    print("📊 修复内容总结:")
    print("✅ 修复了删除插件后返回null的问题")
    print("✅ 删除的插件现在正确返回'插件不存在'错误")
    print("✅ 改进了unique_identifier的匹配逻辑")
    print("✅ 增加了详细的调试日志")
    
    print(f"\n🎯 预期效果:")
    print("1. 删除的插件返回404错误，包含'插件不存在'信息")
    print("2. 存在的插件能正确获取授权表单")
    print("3. firecrawl等marketplace插件的授权表单能正常工作")
    print("4. 返回的数据结构符合接口定义")

def main():
    """主函数"""
    print("开始测试授权表单修复效果...")
    
    test_auth_form_fixes()
    
    print(f"\n💡 如果仍有问题，请检查:")
    print("1. agent-tool-api服务是否已重启")
    print("2. applet-backend服务是否已重启")
    print("3. Dify服务器连接是否正常")
    print("4. 插件的unique_identifier是否正确")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
