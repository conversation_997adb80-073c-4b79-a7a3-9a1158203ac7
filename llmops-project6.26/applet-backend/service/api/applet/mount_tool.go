package applet

import (
	"net/http"

	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/api_tools"
)

func (r *Resource) APIToolService(root string) {
	projectIDQueryParam := r.QueryParameter("project_id", "项目ID")
	r.Path(root).Consumes(restful.MIME_JSON).Produces(restful.MIME_JSON)
	// READ APIs
	tags := []string{"应用仓库-工具集"}
	// 工具集列表
	r.Route(r.GET("/collections").To(r.ListToolCollections).
		Doc("工具集列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []api_tools.APIToolCollectionBaseDO{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	r.Route(r.GET("/collections-describer").To(r.ListPublishedDescriber).
		Doc("工具集列表-智能体使用的结构").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []agent_definition.APIToolCollectionDescriber{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	// 查看工具集详情
	r.Route(r.GET("/collections/{id}").To(r.QueryToolCollectionByID).
		Doc("查看工具集详情").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(r.PathParameter("id", "工具集ID")).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), api_tools.APIToolCollectionDO{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	// 启动dynamic-mcp
	r.Route(r.GET("/collections/{id}/dynamic-mcp").To(r.StartDynamicMcpByID).
		Doc("启动Dynamic MCP").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(r.PathParameter("id", "工具集ID")).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	r.Route(r.POST("/collections/{id}/dynamic-mcp").To(r.StartDynamicMcpByID).
		Doc("启动Dynamic MCP").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(r.PathParameter("id", "工具集ID")).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	r.Route(r.DELETE("/collections/{id}/dynamic-mcp").To(r.StartDynamicMcpByID).
		Doc("启动Dynamic MCP").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(r.PathParameter("id", "工具集ID")).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	// 创建工具集
	r.Route(r.POST("/collections").To(r.CreateToolCollection).
		Doc("创建工具集").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(api_tools.APIToolCollectionDO{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	// 修改工具集
	r.Route(r.PUT("/collections/{id}").To(r.UpdateToolCollection).
		Doc("修改工具集").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Param(r.PathParameter("id", "工具集ID")).
		Reads(api_tools.APIToolCollectionDO{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	// 删除工具集
	r.Route(r.DELETE("/collections/{id}").To(r.DeleteCollection).
		Doc("删除工具集").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	// 批量删除工具集
	r.Route(r.POST("/collections:batch_delete").To(r.BatchDeleteCollection).
		Doc("批量删除工具集").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(helper.IDs{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.IDs{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	// 发布工具集
	r.Route(r.POST("/collections:publish").To(r.PublishCollection).
		Doc("发布工具集").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(helper.ID{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	// 取消发布工具集
	r.Route(r.POST("/collections:cancel_publish").To(r.CancelPublishCollection).
		Doc("取消发布工具集").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(helper.ID{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.ID{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	// 获取工具集示例
	r.Route(r.GET("/collections/-/demos").To(r.ListToolCollectionDemos).
		Doc("工具集示例").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []api_tools.APIToolDemoInfo{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	// 获取MCP工具列表
	r.Route(r.POST("/collections/mcp/tools").To(r.GetMCPServerTools).
		Doc("获得MCP的工具列表").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(api_tools.APIToolCollectionDO{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), []api_tools.APIToolDO{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	// 工具测试
	r.Route(r.POST("/collections:test_api").To(r.TestToolAPI).
		Doc("工具测试").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(api_tools.APIToolTestParamInfo{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.HttpStringResp{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	// 工具调用
	r.Route(r.POST("/collections:call_api").To(r.CallToolAPI).
		Doc("工具调用").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(api_tools.APIToolCallParamInfo{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), helper.HttpStringResp{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))
	// 原始信息转换为结构化信息
	r.Route(r.POST("/collections:parser_meta_api").To(r.ParserMetaData).
		Doc("解析原始API信息（yaml/json）").Metadata(restfulspec.KeyOpenAPITags, tags).
		Param(projectIDQueryParam).
		Reads(api_tools.APIToolMetaInfo{}).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), api_tools.APIToolCollectionDO{}).
		Returns(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), nil))

	// 获取所有已安装插件信息及授权状态
	r.Route(r.GET("/dify-plugins:installed").
		To(r.GetInstalledDifyPlugins).
		Doc("获取所有已安装的dify插件信息").
		Metadata(restfulspec.KeyOpenAPITags, tags).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), InstalledPluginsResponse{}))

	// 上传安装插件
	r.Route(r.POST("/dify-plugins:upload").
		To(r.UploadDifyPlugin).
		Doc("上传并安装dify插件").
		Consumes("multipart/form-data").
		Metadata(restfulspec.KeyOpenAPITags, tags).
		Returns(http.StatusOK, http.StatusText(http.StatusOK), PluginUploadResponse{}))
}
