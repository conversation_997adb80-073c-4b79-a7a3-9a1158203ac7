package applet

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/aws/smithy-go/ptr"
	"github.com/cockroachdb/errors"
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/clients"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/core/api_tool"
	"transwarp.io/applied-ai/applet-backend/core/knowledge_base"
	clients2 "transwarp.io/applied-ai/applet-backend/pkg/clients"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/pkg/models/agent_definition"
	"transwarp.io/applied-ai/applet-backend/pkg/models/api_tools"
	"transwarp.io/applied-ai/mcp-go/mcp"
	"transwarp.io/applied-ai/mcp-go/server"
)

func getCollectionIDFromPath(request *restful.Request) (string, error) {
	collectionID := request.PathParameter("id")
	if collectionID == "" {
		return collectionID, stderr.InvalidParam.Error("collection id is empty")
	}
	return collectionID, nil
}

func (r *Resource) QueryToolCollectionByID(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	collectionID, err := getCollectionIDFromPath(request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	res, err := api_tool.ToolManager.GetToolCollectionByID(ctx, collectionID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}

func (r *Resource) StartDynamicMcpByID(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	projectId := helper.GetProjectID(ctx)
	collectionID, err := getCollectionIDFromPath(request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	token, err := helper.GetToken(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	res, err := api_tool.ToolManager.GetToolCollectionByID(ctx, collectionID)
	if res.BaseInfo.ServerType != agent_definition.ServerTypeMCP && res.BaseInfo.ServerType != agent_definition.ServerTypeDynamicMCP {
		helper.ErrorResponse(response, errors.New("the collection is not mcp or a dynamic mcp"))
		return
	}
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	// if res.BaseInfo.ServerType == agent_definition.ServerTypeDynamicMCP {
	mcpServer := server.NewMCPServer(
		res.BaseInfo.Name,
		"1.0.0",
	)

	// 将每个工具转换为 MCP tool
	for _, tool := range res.Tools {
		mcpTool := mcp.Tool{
			Name:        tool.Name,
			Description: tool.Desc,
			InputSchema: mcp.ToolInputSchema{
				Type:       "object",
				Properties: make(map[string]interface{}),
				Required:   make([]string, 0),
			},
		}

		// 转换参数
		for _, param := range tool.ParamValues {
			property := map[string]interface{}{
				"type":        param.ParamValueType,
				"description": param.Desc,
			}
			if param.DefaultValue != nil {
				property["default"] = param.DefaultValue
			}
			mcpTool.InputSchema.Properties[param.Name] = property
			if param.Required {
				mcpTool.InputSchema.Required = append(mcpTool.InputSchema.Required, param.Name)
			}
		}

		mcpServer.AddTool(mcpTool, func(mcpCtx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			// 校验必填参数
			for _, paramName := range mcpTool.InputSchema.Required {
				if _, exists := req.Params.Arguments.(map[string]interface{})[paramName]; !exists {
					return nil, fmt.Errorf("missing required parameter: %s", paramName)
				}
			}

			// 获取 restPath 参数
			apiUrl := tool.Path

			// 构造请求参数
			params := make(map[string]interface{})
			for paramName, param := range mcpTool.InputSchema.Properties {
				params[paramName] = req.Params.Arguments.(map[string]interface{})[paramName]
				if params[paramName] == nil && param.(map[string]interface{})["default"] != nil {
					params[paramName] = param.(map[string]interface{})["default"]
				}
			}

			// connection params
			if res.Headers == nil {
				res.Headers = map[string]string{}
			}
			for _, p := range res.Params {
				value := request.Request.Header.Get(p.Name)
				if value != "" {
					res.Headers[p.Name] = fmt.Sprintf("%v", value)
				}
			}

			queryParams := make(map[string]string)
			if res.BaseInfo.ID == "builtin-tool-wiki-dynamic-mcp" {
				if tool.ID == "builtin-wiki-search-tool" {
					cql := fmt.Sprintf("text ~ \"%s\"", params["keyword"].(string))
					params = map[string]interface{}{"cql": cql, "limit": params["limit"]}
				}
				if tool.ID == "builtin-wiki-document-tool" {
					apiUrl = strings.ReplaceAll(apiUrl, "{id}", params["doc_id"].(string))
					params = map[string]interface{}{"expand": "body.storage"}
				}
			}

			if res.BaseInfo.ID == "builtin-tool-kb-dynamic-mcp" {
				queryParams = map[string]string{"project_id": projectId}
				if tool.ID == "builtin-get-kb-list-tool" {
					params = map[string]interface{}{"project_id": projectId, "is_published_selector": "true"}
				}
				if tool.ID == "builtin-get-kb-document-tool" {
					apiUrl = strings.ReplaceAll(apiUrl, "{kb_id}", params["kb_id"].(string))
					params = map[string]interface{}{"project_id": projectId}
				}
				res.Headers["Authorization"] = token
			}

			// 模拟发送 REST 请求
			var resp string
			switch tool.Alias {
			case "应用链工具":
				resp, err = sendServiceClientRequest(ctx, apiUrl, params)
				if err != nil {
					return nil, fmt.Errorf("failed to execute Service request: %v", err)
				}
			case "知识库工具":
				resp, err = sendKnowledgeClientRequest(ctx, apiUrl, params)
				if err != nil {
					return nil, fmt.Errorf("failed to execute Knowledge request: %v", err)
				}
			case "dify插件工具":
				resp, err = sendDifyPluginRequest(ctx, tool.Name, params)
				if err != nil {
					return nil, fmt.Errorf("failed to execute Dify plugin request: %v", err)
				}
			default:
				if strings.ToUpper(tool.Method) == "GET" {
					for k, v := range params {
						queryParams[k] = v.(string)
					}
					resp, err = sendGeneralHttpRequest(ctx, tool.Method, apiUrl, queryParams, nil, res.Headers, res.ProxyInfo)
				} else {
					resp, err = sendGeneralHttpRequest(ctx, tool.Method, apiUrl, queryParams, params, res.Headers, res.ProxyInfo)
				}
				if err != nil {
					return nil, fmt.Errorf("failed to execute general http request: %v", err)
				}
			}

			if res.BaseInfo.ID == "builtin-tool-wiki-dynamic-mcp" {
				var respMap map[string]interface{}
				if err := json.Unmarshal([]byte(resp), &respMap); err != nil {
					return nil, fmt.Errorf("failed to unmarshal response: %v", err)
				}

				// 获取results数组
				results, ok := respMap["results"].([]interface{})
				if !ok {
					return nil, fmt.Errorf("invalid response format: results field not found or not an array")
				}
				var simplifiedResults []map[string]interface{}
				if tool.ID == "builtin-wiki-search-tool" {
					// 提取每个结果的id、title和_links
					for _, result := range results {
						if resultMap, ok := result.(map[string]interface{}); ok {
							simplifiedResults = append(simplifiedResults, map[string]interface{}{
								"id":     resultMap["id"],
								"title":  resultMap["title"],
								"_links": resultMap["_links"],
							})
						}
					}
				}

				if tool.ID == "builtin-wiki-document-tool" {
					// 提取每个结果的id、title和value
					for _, result := range results {
						if resultMap, ok := result.(map[string]interface{}); ok {
							body, ok := resultMap["body"].(map[string]interface{})
							if !ok {
								continue
							}
							storage, ok := body["storage"].(map[string]interface{})
							if !ok {
								continue
							}
							value, ok := storage["value"].(string)
							if !ok {
								continue
							}
							simplifiedResults = append(simplifiedResults, map[string]interface{}{
								"id":    resultMap["id"],
								"title": resultMap["title"],
								"value": value,
							})
						}
					}
				}

				// 构造新的响应
				newResp := map[string]interface{}{
					"results": simplifiedResults,
				}

				// 将新响应转换为JSON字符串
				newRespBytes, err := json.Marshal(newResp)
				if err != nil {
					return nil, fmt.Errorf("failed to marshal new response: %v", err)
				}
				resp = string(newRespBytes)
			}
			return mcp.NewToolResultText(resp), nil
		})
	}

	// 创建 SSE 服务器
	sseServer := server.NewStreamableHTTPServer(mcpServer)
	sseServer.ServeHTTP(response.ResponseWriter, request.Request)
}

func sendServiceClientRequest(ctx context.Context, serviceId string, params map[string]interface{}) (string, error) {
	resp, err := clients.AppletSvcCli.CallAppletSvc(ctx, serviceId, params)
	if err != nil {
		return "", fmt.Errorf("failed to execute the application service: %v", err)
	}

	return resp.Body, nil
}

// sendRestRequest 处理普通的 HTTP 请求
func sendKnowledgeClientRequest(ctx context.Context, knowledgeBaseId string, params map[string]interface{}) (string, error) {
	// 构造知识库检索请求
	testProto := new(pb.RetrieveKnowledgeBaseReq)
	err := stdsrv.UnmarshalMixWithProto(params, testProto)
	if err != nil {
		return "", fmt.Errorf("failed to unmarshall params: %v", err)
	}
	testProto.KnowledgeBaseId = knowledgeBaseId

	// 调用知识库检索服务
	rsp, err := knowledge_base.GetKnowledgeBaseManager().RetrieveKnowledgeBase(ctx, testProto)
	if err != nil {
		return "", fmt.Errorf("failed to retrieve knowledge base: %v", err)
	}

	// 处理检索结果
	rspJson, err := json.Marshal(rsp)
	if err != nil {
		return "", fmt.Errorf("failed to marshall response: %v", err)
	}
	return string(rspJson), nil
}

func sendGeneralHttpRequest(ctx context.Context, method string, restPath string, queryParams map[string]string, reqMap map[string]interface{}, headers map[string]string, proxyInfo *agent_definition.ProxyInfo) (string, error) {
	reqBody := ""
	if len(reqMap) > 0 {
		bytes, err := json.Marshal(reqMap)
		if err != nil {
			return "", err
		}
		reqBody = string(bytes)
	}

	headerParams := make(map[string]string)
	for k, v := range headers {
		headerParams[k] = v
	}

	var proxy *agent_definition.ProxyInfo
	httpCli := clients.HttpCli
	// 先取默认值
	if conf.Config.APIToolConfig.DefaultProxy.Url != "" {
		schemaValue := pb.ProxyScheme_value[conf.Config.APIToolConfig.DefaultProxy.Schema]
		proxy = &agent_definition.ProxyInfo{
			Scheme: pb.ProxyScheme(schemaValue),
			Url:    conf.Config.APIToolConfig.DefaultProxy.Url,
		}
	}
	// 再从接口取
	if proxyInfo != nil && proxyInfo.Url != "" {
		proxy = proxyInfo
	}
	if proxy != nil {
		transport, err := clients2.HttpTransport(proxy)
		if err != nil {
			return "", err
		}
		httpCli = clients2.NewHttpCliWithProxy(transport)
	}
	return httpCli.HttpCallString(ctx, &clients2.HttpParam{
		Method:     strings.ToUpper(method),
		Url:        restPath,
		ReqBody:    reqBody,
		Header:     headerParams,
		QueryParam: queryParams,
	})
}

func (r *Resource) ListToolCollections(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	res, err := api_tool.ToolManager.ListToolCollectionBaseInfos(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}

func (r *Resource) ListPublishedDescriber(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	res, err := api_tool.ToolManager.ListPublishedDescriber(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}

func (r *Resource) ListToolCollectionDemos(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	res, err := api_tool.ToolManager.ListCollectionDemos(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}

// GetMCPServerTools 获取MCP服务端工具集
func (r *Resource) GetMCPServerTools(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	collectionDO := &api_tools.APIToolCollectionDO{}
	err := request.ReadEntity(&collectionDO)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	res, err := api_tool.ToolManager.GetMCPServerTools(ctx, collectionDO)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)
}

func (r *Resource) CreateToolCollection(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	collectionDO := &api_tools.APIToolCollectionDO{}
	err := request.ReadEntity(&collectionDO)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	res, err := api_tool.ToolManager.CreateAPIToolCollection(ctx, collectionDO)
	if collectionDO.BaseInfo.ServerType == agent_definition.ServerTypeDynamicMCP {
		collectionDO.BaseURL = strings.ReplaceAll(collectionDO.BaseURL, "{id}", res)
	}
	err = api_tool.ToolManager.UpdateAPIToolCollectionByID(ctx, res, collectionDO)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.ID{ID: res})
}

func (r *Resource) UpdateToolCollection(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	collectionID, err := getCollectionIDFromPath(request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	collectionDO := &api_tools.APIToolCollectionDO{}
	err = request.ReadEntity(&collectionDO)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	existCollection, err := api_tool.ToolManager.GetToolCollectionByID(ctx, collectionID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	if ptr.ToBool(existCollection.BaseInfo.Released) && existCollection.BaseInfo.ServerType != agent_definition.ServerTypeDynamicMCP {
		helper.ErrorResponse(response, helper.ToolUpdateVerifyErr.Error("released tool can not update"))
		return
	}
	err = api_tool.ToolManager.UpdateAPIToolCollectionByID(ctx, collectionID, collectionDO)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.ID{
		ID: collectionID,
	})
}

func (r *Resource) ParserMetaData(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	metaInfo := api_tools.APIToolMetaInfo{}
	err := request.ReadEntity(&metaInfo)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	res, err := api_tool.ToolManager.ParserMeta(ctx, metaInfo.Type, metaInfo.Meta)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, res)

}

func (r *Resource) DeleteCollection(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	collectionID, err := getCollectionIDFromPath(request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	err = api_tool.ToolManager.BatchDelCollectionsByIDs(ctx, []string{collectionID})
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.ID{ID: collectionID})

}

func (r *Resource) BatchDeleteCollection(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	IDs := helper.IDs{}
	if err := request.ReadEntity(&IDs); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	err := api_tool.ToolManager.BatchDelCollectionsByIDs(ctx, IDs.IDs)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, IDs)

}

func (r *Resource) PublishCollection(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	ID := helper.ID{}
	if err := request.ReadEntity(&ID); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	err := api_tool.ToolManager.PublishCollectionByID(ctx, ID.ID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, ID)

}

func (r *Resource) CancelPublishCollection(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	ID := helper.ID{}
	if err := request.ReadEntity(&ID); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	err := api_tool.ToolManager.CancelPublishCollectionByID(ctx, ID.ID)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, ID)

}

func (r *Resource) TestToolAPI(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	param := &api_tools.APIToolTestParamInfo{}
	if err := request.ReadEntity(&param); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	res, err := api_tool.ToolManager.TestToolAPI(ctx, param)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.HttpStringResp{RespBody: res})

}

func (r *Resource) CallToolAPI(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	param := &api_tools.APIToolCallParamInfo{}
	if err := request.ReadEntity(&param); err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	res, err := api_tool.ToolManager.CallToolAPI(ctx, param)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, helper.HttpStringResp{RespBody: res})

}

// sendDifyPluginRequest 处理Dify插件请求
func sendDifyPluginRequest(ctx context.Context, toolName string, params map[string]interface{}) (string, error) {
	// 使用与原有openapi插件相同的地址配置方式
	openapiURL := conf.Config.APIToolConfig.BuiltinOpenapi.Url
	var agentToolAPIURL string
	if openapiURL != "" {
		// 从openapi URL中提取基础地址
		if idx := strings.Index(openapiURL, "/v1/"); idx != -1 {
			agentToolAPIURL = openapiURL[:idx]
		} else {
			agentToolAPIURL = "http://172.17.124.17:30790"
		}
	} else {
		agentToolAPIURL = "http://172.17.124.17:30790"
	}

	// 构造请求URL - 这里需要根据实际的dify插件API结构来调整
	// 假设dify插件有统一的调用接口
	requestURL := fmt.Sprintf("%s/v1/dify-plugins/tools/%s/invoke", agentToolAPIURL, toolName)

	// 构造请求体
	requestBody, err := json.Marshal(params)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request params: %v", err)
	}

	// 发送HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", requestURL, strings.NewReader(string(requestBody)))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("dify plugin API returned status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %v", err)
	}

	return string(body), nil
}
