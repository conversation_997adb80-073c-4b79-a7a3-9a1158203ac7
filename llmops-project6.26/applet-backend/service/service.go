package service

import (
	"net/http"
	"strings"
	"time"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/examine"
	"transwarp.io/applied-ai/applet-backend/service/api/applet"
	"transwarp.io/applied-ai/applet-backend/service/api/dialog"
	"transwarp.io/applied-ai/applet-backend/service/api/guardrails"
	"transwarp.io/applied-ai/applet-backend/service/api/knowledge_base"

	"transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/applet-backend/conf"
	"transwarp.io/applied-ai/applet-backend/pkg/helper"
	"transwarp.io/applied-ai/applet-backend/service/api/swagger"
)

const (
	API_ROOT = "/api/v1"

	AppletURIs = "/applet"

	SwaggerURIs = "/apidocs"
)

// 不需要进行权限验证的'具体'URI列表
var exceptURIs = map[string]struct{}{}

// 不需要进行权限验证的模块
var exceptModules = map[string]struct{}{
	SwaggerURIs: {},
	AppletURIs:  {},
}

func Init() {
	restful.RegisterEntityAccessor(restful.MIME_JSON, stdsrv.DefaultProtoJsonAccessor())
}

// MountBaseAPIModule 挂载基础API模块
func MountBaseAPIModule() {
	restful.Add(swagger.NewAPI(SwaggerURIs))
}

func MountCompleteAPIModule() {
	wss := []*restful.WebService{
		applet.NewAppletAPI(API_ROOT, "/applet"),
		applet.NewAPIToolAPI(API_ROOT, "/tool"),
		applet.NewApplicationAPI(API_ROOT, "/app"),
		applet.NewGeneralLLMAPI(API_ROOT, "/global-llm"),
		knowledge_base.NewAPI(API_ROOT + "/knowlhub"),
		guardrails.NewAPI(API_ROOT, "/guardrails"),
		applet.NewPortalInfoAPI(API_ROOT, "/portal-info"),
		dialog.NewDialogAPI(API_ROOT, "/dialog"),
	}
	for _, ws := range wss {
		ws.Filter(AuthCheck)
		ws.Filter(FormatLogger)
		ws.Filter(examine.ExamineCheck)
		restful.Add(ws)
	}
}

func UnmountEdgeCompletedAPIModule() {
	restful.DefaultContainer = restful.NewContainer()
	MountBaseAPIModule()

	for _, ws := range restful.RegisteredWebServices() {
		ws.Filter(FormatLogger)
		ws.Filter(AuthCheck)
	}
}

func FormatLogger(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
	now := time.Now()
	// 适配 TDC-Ingress 跳转
	spaBasePath := req.HeaderParameter(conf.Config.SpaBasePath)
	if spaBasePath != "" {
		cookie := http.Cookie{
			Name:  "basename",
			Value: spaBasePath,
			Path:  "/",
		}
		resp.AddHeader("Set-Cookie", cookie.String())
	}
	chain.ProcessFilter(req, resp)

	realRemoteAddr := req.Request.Header.Get("X-Remote-Addr")
	if realRemoteAddr == "" {
		realRemoteAddr = req.Request.RemoteAddr
	}
	stdlog.Infof("%s %s %s %s %s %d %d %v",
		strings.Split(realRemoteAddr, ":")[0],
		parseUsername(req),
		req.Request.Method,
		req.Request.URL.RequestURI(),
		req.Request.Proto,
		resp.StatusCode(),
		resp.ContentLength(),
		time.Now().Sub(now),
	)
}
func parseUsername(r *restful.Request) string {
	var username string
	user := auth.GetAuthContext(r)
	if user == nil || user.GetUsername() == "" {
		username = "-"
	} else {
		username = user.GetUsername()
	}
	return username
}

// AuthCheck 在执行API处理函数前对用户授权信息进行验证
func AuthCheck(request *restful.Request, response *restful.Response, chain *restful.FilterChain) {
	// 启用用户校验的情况下， 对所有需要验证的 API 调用进行验证
	if !conf.Config.DisableAuth && needCheckURI(request.Request.RequestURI) {
		token, err := auth.ParseTokenFromRequest(request.Request)
		if err != nil {
			helper.ErrorResponse(response, stderr.Unauthenticated.Error(err.Error()))
			return
		}
		auth.SetAuthContext(request, &token)
	}

	// 禁用了用户校验的情况下，填充默认的用户信息
	if conf.Config.DisableAuth {
		auth.SetDefaultAuthContext(request)
	}
	chain.ProcessFilter(request, response)
}

// needCheckURI 返回是否对URI进行授权检查
func needCheckURI(uri string) bool {
	for exceptURI := range exceptURIs {
		if strings.HasPrefix(uri, exceptURI) {
			return false
		}
	}
	for module := range exceptModules {
		if strings.HasPrefix(uri, module) {
			return false
		}
	}
	return true
}
