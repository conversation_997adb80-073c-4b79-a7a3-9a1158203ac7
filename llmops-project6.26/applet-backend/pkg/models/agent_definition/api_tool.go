package agent_definition

import (
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/triton"
)

type APIToolParamType string

const (
	APIToolParamTypeQuery  APIToolParamType = "query"
	APIToolParamTypePath   APIToolParamType = "path"
	APIToolParamTypeHeader APIToolParamType = "header"
	APIToolParamTypeBody   APIToolParamType = "body"
	APIToolParamTypeArray  APIToolParamType = "array"
)

type McpType string

const (
	McpTypeSse            McpType = "sse"
	McpTypeStreamableHttp McpType = "streamableHttp"
)

type ServerType string

const (
	ServerTypeRest       ServerType = "rest"
	ServerTypeMCP        ServerType = "mcp"
	ServerTypeDynamicMCP ServerType = "dynamic_mcp"
	ServerTypeDify       ServerType = "dify"
)

type ProxyInfo struct {
	Scheme pb.ProxyScheme `json:"scheme,omitempty"`
	Url    string         `json:"url,omitempty"`
}

// APIToolCollectionDescriber 智能体工具定义
type APIToolCollectionDescriber struct {
	ID         string             `json:"id" description:"数据库ID"`         // 数据库ID
	Name       string             `json:"name" description:"工具集名字"`       // 工具集名字
	Desc       string             `json:"desc" description:"工具集描述"`       // 工具集描述
	AgentTools []APIToolDescriber `json:"agent_tools" description:"工具列表"` // 工具列表
}

type APIToolDescriber struct {
	ID                      string            `json:"id" description:"apiID"`                               // apiID
	BaseURL                 string            `json:"base_url" description:"基础地址"`                          // 基础地址
	Method                  string            `json:"method" description:"http方法 get/post等"`                // http method
	CollectionHeaders       map[string]string `json:"headers" description:"全局header"`                       // 全局header
	APIPath                 string            `json:"api_path" description:"api路径"`                         // api路径
	Name                    string            `json:"name" description:"api名字"`                             // api名字
	Desc                    string            `json:"desc" description:"工具描述"`                              // 工具描述
	Params                  []APIToolParam    `json:"params" description:"api参数列表"`                         // api参数
	CollectionId            string            `json:"collection_id" description:"工具集id"`                    // 工具集id
	CollectionName          string            `json:"collection_name" description:"工具集名称"`                  // 工具集名称
	CollectionParams        []MCPParam        `json:"collection_params" description:"mcp参数"`                // 插件参数
	CollectionParamValueMap map[string]any    `json:"collection_param_value_map" description:"mcp参数键值对"`    // 插件参数赋值
	ToolParamValueMap       map[string]any    `json:"tool_param_value_map" description:"用户自定义参键值对"`         // 用户自定义参数赋值
	ServerType              ServerType        `json:"server_type" description:"服务器类型，rest/mcp/dynamic_mcp"` // 插件类型
	Proxy                   ProxyInfo         `json:"proxy" description:"代理"`                               // 代理
	McpType                 McpType           `json:"mcp_type" description:"mcp类型, sse/streamableHttp"`     // mcp server类型
}

// APIToolParam 工具参数信息
type APIToolParam struct {
	Name           string           `json:"name" description:"参数名"`                                                  // 参数名
	Desc           string           `json:"desc" description:"描述"`                                                   // 描述
	ParamValueType string           `json:"param_value_type" description:"数据类型 integer/string/array/object/boolean"` // 数据类型
	ParamType      APIToolParamType `json:"type" description:"类型，query、path、header、body、array"`                      // 类型，query、path、header、body
	Required       bool             `json:"required" description:"是否必传"`                                             // 是否必传
	DefaultValue   any              `json:"default_value" description:"默认值"`                                         // 默认值
	ModelIgnore    bool             `json:"model_ignore" description:"模型是否忽略"`                                       // 模型是否需要
}

// MCPParam mcp插件参数
type MCPParam struct {
	Name           string `json:"name" description:"参数名"`                                                         // 参数名
	DefaultValue   any    `json:"default_value" description:"默认值"`                                                // 默认值
	ParamValueType string `json:"param_value_type" description:"数据类型 integer/number/string/array/object/boolean"` // 数据类型
	Desc           string `json:"desc" description:"描述"`                                                          // 描述
	Encryption     bool   `json:"encryption" description:"是否加密"`                                                  // 是否加密
	Display        bool   `json:"display" description:"是否展示参数"`                                                   // 是否展示参数
	Customize      bool   `json:"customize" description:"是否支持自定义"`                                                // 是否支持自定义
}

// Type 当前工具类型，可用于判断为其匹配哪个执行器
func (a APIToolDescriber) Type() ToolType {
	return ToolTypeAPITool
}

func (a APIToolDescriber) Definition() Tool {
	params := make(map[string]triton.ParameterProperty, 0)
	required := make([]string, 0)
	for _, p := range a.Params {
		if !p.ModelIgnore {
			required = append(required, p.Name)
			params[p.Name] = triton.ParameterProperty{
				Type:        triton.ParameterType(p.ParamValueType),
				Description: p.Desc,
			}
		}
	}
	return Tool{
		ID:           a.ID,
		Type:         a.Type(),
		NameForModel: a.Name,
		NameForHuman: a.Name,
		Description:  a.Desc,
		Parameters: triton.FunctionParameters{
			Type:       triton.ParameterTypeObject,
			Properties: params,
			Required:   required,
		},
	}
}
