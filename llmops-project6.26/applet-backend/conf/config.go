package conf

import (
	"sort"
	"strings"
	"time"

	v1 "k8s.io/api/core/v1"

	"transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/applied-ai/applet-backend/pkg/clients"

	boot "transwarp.io/applied-ai/aiot/vision-std/boot/conf"
	"transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

type (
	DatabaseType   = string
	ImageCacheType string
)

const (
	DatabaseMysql  DatabaseType = "mysql"
	DatabaseSqlite DatabaseType = "sqlite"
	MB                          = 1024 * 1024
	EnvArraySep    string       = "," // 环境变量数组的分割符

	NamespacePlaceholder string = "{namespace}"
)

type AppConfig struct {
	// 基础配置
	EdgeId       string       `yaml:"edge_id"`        // 当前节点的节点ID
	EdgeHost     string       `yaml:"edge_host"`      // 当前节点的地址或域名
	IsSimpleMode bool         `yaml:"is_simple_mode"` // 轻量级运行模式
	DisableAuth  bool         `yaml:"disable_auth"`   // 是否禁用用户登录系统，请勿用于生产模式
	SpaBasePath  string       `yaml:"spa_base_path"`  // 适配Ingress跳转的BasePath
	Server       ServerConfig `yaml:"server"`         // 系统服务的相关配置
	StorageRoot  string       `yaml:"storage_root"`   // 存储的根目录，对应sfs/store
	Token        string       `yaml:"token"`          // 服务间请求使用的token

	// 公共组件配置
	Database         DatabaseType         `json:"database" yaml:"database"`
	Mysql            conf.MysqlConfig     `yaml:"mysql"`
	Transport        conf.TransportConfig `yaml:"transport"`
	Mqtt             conf.MqttConfig      `yaml:"mqtt"`
	Sqlite           conf.SqliteConfig    `json:"sqlite" yaml:"sqlite"`
	Redis            conf.RedisConfig     `json:"redis" yaml:"redis"`
	License          conf.LicenseConfig   `yaml:"license"`
	InfluxDB         conf.InfluxdbConfig  `yaml:"influxdb"`
	MWH              MWHConfig            `yaml:"mwh"`
	CVATConfig       CVATConfig           `yaml:"cvat"`
	MLOps            clients.MLOpsConfig  `yaml:"mlops"`
	GrpcConfig       GrpcConfig           `yaml:"grpc_config"` // GRPC配置
	HippoConfig      HippoConfig          `yaml:"hippo_config"`
	ScopeConfig      ScopeConfig          `yaml:"scope_config"`
	TKHConfig        TKHConfig            `yaml:"tkh_config"`
	DocSvcConfig     DocSvcConfig         `yaml:"doc_svc_config"` // 文档处理算子服务配置
	GuardrailsConfig GuardrailsConfig     `yaml:"guardrails"`     // 安全围栏配置
	HybaseConfig     HybaseConfig         `yaml:"hybase_config"`
	DocEngineConfig  DocEngineConfig      `yaml:"doc_engine_config"`

	// 各模块独立配置
	ThingerLog       ThingerLog `yaml:"thinger_log"`
	GrpcServerConfig GrpcConfig `yaml:"grpc_config"`
	// Engine     boot.ManagerConfig `yaml:"engine"`
	Kapacitor KapacitorConfig `yaml:"kapacitor_config"`
	// prompt client的参数
	PromptCfg PromptClientCfg `yaml:"prompt_config"`

	CSMClientCfg CSMClientCfg `yaml:"csm_config"`

	CasConfig CasConfig `yaml:"cas_config"`

	Engine boot.ManagerConfig `yaml:"engine"`

	ChainDeployCfg ChainDeployConfig `yaml:"deploy_config"`

	AgentConfig AgentConfig `yaml:"default_agent_config"`

	KnowlhubConfig KnowlhubConfig `yaml:"knowlhub"`
	AppSvcConfig   AppSvcConfig   `yaml:"app_svc_config"`
	APIToolConfig  APIToolCfg     `yaml:"api_tool"`
	InvokeConfig   InvokeConfig   `yaml:"invoke_config"`

	// 代码服务配置
	CodeService CodeServiceConfig `yaml:"code_service"`
}

type CodeServiceConfig struct {
	TestPythonUrl string `yaml:"test_python_url"` // Python代码测试服务地址
	MaxInputLen   int    `yaml:"max_input_len"`   // 提示词中input的最大长度限制
}

type GuardrailsConfig struct {
	FullUpdateAddr string `json:"full_update_addr" yaml:"full_update_addr"`
}

type MWHConfig struct {
	Host        string `yaml:"host"`
	GrpcPort    string `yaml:"grpc_port"`
	DefaultUser string `yaml:"default_user"`
}

type CVATConfig struct {
	Host     string `yaml:"host"`
	GrpcPort string `yaml:"grpc_port"`
	HttpPort string `yaml:"http_port"`
}

type KapacitorConfig struct {
	Addr           string `json:"addr" yaml:"addr"`
	DBName         string `json:"database" yaml:"database"`
	APIPath        string `json:"api_path" yaml:"api_path"`
	APIHealthPath  string `json:"health_api_path" yaml:"health_api_path"`
	APIPort        int    `json:"api_port" yaml:"api_port"`
	ExecuteAPIPort string `json:"execute_api_port" yaml:"execute_api_port"`
}

type PromptClientCfg struct {
	Host string `json:"host" yaml:"host"`
	Port string `json:"port" yaml:"port"`
	// Auth string `json:"auth" yaml:"auth"`
}

type CSMClientCfg struct {
	Host     string `json:"host" yaml:"host"`
	HttpPort string `json:"http_port" yaml:"http_port"`
	GrpcPort string `json:"grpc_port" yaml:"grpc_port"`
	// Auth     string `json:"auth" yaml:"auth"`
}

type APIToolCfg struct {
	BuiltinToolBaseUrl    string            `yaml:"builtin_tool_base_url"`
	DefaultProxy          ProxyCfg          `yaml:"default_proxy"`
	BuiltinOpenapi        BuiltinOpenapiCfg `yaml:"builtin_openapi"`
	BuiltinAllToolsSwitch bool              `yaml:"builtin_all_tools_switch"`
}

type ProxyCfg struct {
	Schema string `yaml:"schema"`
	Url    string `yaml:"url"`
}

type BuiltinOpenapiCfg struct {
	Url string `yaml:"url"`
}

type ServerConfig struct {
	Addr string `json:"addr" yaml:"addr"`

	SocketIORoom string        `json:"socket_io_room" yaml:"socket_io_room"`
	HttpTimeout  time.Duration `json:"http_timeout" yaml:"http_timeout"`
}

type ThingerLog struct {
	// 日志打印配置
	Logger stdlog.Config `json:"logger" yaml:"logger"`
	// 所有日志文件所在目录, 包括 应用实例, 函数服务, 模型等容器的日志
	Path string `json:"path" yaml:"path"`
}

type EdgeConfig struct {
	ApiToken            string `yaml:"api_token"`
	MdlSrvCallUrlFormat string `yaml:"mdl_srv_call_url_format"`
}

type GrpcConfig struct {
	MaxMessageMB int    `yaml:"max_message_mb"`
	ServerHost   string `yaml:"server_host"`
	ServerPort   string `yaml:"server_port"`
}

type HippoConfig struct {
	Port              string `yaml:"port"`
	UserName          string `yaml:"username"`
	Password          string `yaml:"password"`
	Database          string `yaml:"database"`
	DefaultMetricType string `yaml:"default_metric_type"`
	DefaultIndexType  string `yaml:"default_index_type"`
	MaxChunkPageSize  int    `yaml:"max_chunk_page_size"` // 获取段落内容时，分页的最大pagesize
}

type ScopeConfig struct {
	Port     string `yaml:"port"`
	UserName string `yaml:"username"`
	Password string `yaml:"password"`
}

type HybaseConfig struct {
	Url      string            `yaml:"url"`
	Username string            `yaml:"username"`
	Password string            `yaml:"password"`
	Timeout  int64             `yaml:"timeout"`
	Params   map[string]string `yaml:"params"`
	Replicas int               `yaml:"replicas"`
}

type DocEngineConfig struct {
	Address string `yaml:"address"`
	Port    int32  `yaml:"port"`
}

type ModelServiceConfig struct {
	FullUrl   string `yaml:"full_url"`
	Dimension int    `yaml:"dimension"`
}

type TKHConfig struct {
	Enabled      bool   `yaml:"enabled"`
	Host         string `yaml:"host"`
	Schema       string `yaml:"schema"`
	BaseURL      string `yaml:"base_url"`
	GetTokenUrl  string `yaml:"get_token_url"`
	GetTokenBody string `yaml:"get_token_body"`
}

type HttpApiEndpoint struct {
	Url    string `yaml:"url"`
	Method string `yaml:"method"`
}

// https://sophon-mleng.yuque.com/ffogg5/solar/kzsvvcb4pgt2g11v#V55B3
type DocSvcConfig struct {
	Address      string          `yaml:"address"`
	LoadApi      HttpApiEndpoint `yaml:"load_api"`
	SplitApi     HttpApiEndpoint `yaml:"split_api"`
	LoadSplitApi HttpApiEndpoint `yaml:"load_split_api"`
	LoadrawApi   HttpApiEndpoint `yaml:"loadraw_api"`
	SplitrawApi  HttpApiEndpoint `yaml:"splitraw_api"`
	LoadChunkApi HttpApiEndpoint `yaml:"load_chunk_api"`
	ChunkApi     HttpApiEndpoint `yaml:"chunk_api"`
	LoadTableApi HttpApiEndpoint `yaml:"load_table_api"`
	HealthzApi   HttpApiEndpoint `yaml:"healthz_api"`

	DefaultStrategy     string `yaml:"default_strategy"`
	DefaultSplitType    string `yaml:"default_split_type"`
	DefaultChunkSize    int    `yaml:"default_chunk_size"`
	DefaultChunkOverlap int    `yaml:"default_chunk_overlap"`
}

type ChainDeployConfig struct {
	BaseImageConf   `yaml:"base_image_conf"`
	SfsLocalRoot    string `yaml:"sfs_local_root"`
	PVCMountCFGs    string `yaml:"pvc_mount_cfgs"`
	CallBackBaseUrl string `yaml:"call_back_base_url"`
	SfsPvcMountPath string `yaml:"sfs_pvc_mount_path"`
	// 依赖服务
	Redis                   conf.RedisConfig `yaml:"redis"`
	KapacitorHostName       string           `yaml:"kapacitor_host_name"`
	KapacitorInfluxdbUrl    string           `yaml:"kapacitor_influxdb_url"`
	KapacitorMqttUrl        string           `yaml:"kapacitor_mqtt_url"`
	EngineMqttUrl           string           `yaml:"engine_mqtt_url"`
	EngineKnowledgeBasePort string           `yaml:"engine_knowledge_base_port"`
	EngineKnowledgeBaseHost string           `yaml:"engine_knowledge_base_host"`
	EngineMlopsHost         string           `yaml:"engine_mlops_host"`
	EngineRunCodeUrl        string           `yaml:"engine_run_code_url"`
	EngineSecurityUrl       string           `yaml:"engine_security_url"`
}

type BaseImageConf struct {
	// Cpus specified how much of the available CPU resources a instance task container can use.
	CpuLimit string `yaml:"cpu_limit"`

	CpuRequest string `yaml:"cpu_request"`

	// EnableGpu is the resource conf of instance task container.
	// Container will use nvidia runtime while EnableGpu is true.
	EnableGpu bool `yaml:"enable_gpu"`

	// Privileged 是否使用Privileged启动实例容器
	Privileged bool `yaml:"privileged"`

	// BaseImage is the base image address used to start instance task
	BaseImage string `yaml:"base_image"`

	MemRequest string `yaml:"mem_request"`

	// MemLimit is the limit of memory for each instance task
	MemLimit string `yaml:"mem_limit"`

	// SwapLimit is the limit of swap for each instance task
	SwapLimit string `yaml:"swap_limit"`

	// Restart is the restart policy of instance task container,
	// can be any of the following : no, on-failure, always
	Restart string `yaml:"restart"`

	// Envs is the environments variable of task instance container.
	// e.g. `ENV1=value1,ENV2=value2,ENV3=value3`
	Envs string `yaml:"envs"`

	// Volumes is the volumes should be mount in task instance container.
	// K8S 模式下，该部分volume将全部基于指定的PVC进行挂载
	// e.g. Volumes="vision/store/images:/mnt/images" PvcName="vision-volume" 的情况下
	// 将以如下形式进行挂载：
	//  volumeMounts:
	//	- mountPath: /mnt/images
	//	  name: vision-volume
	//	  subPath: vision/store/images
	//
	//  volumes:
	//  - name: vision-volume
	//    persistentVolumeClaim:
	//      claimName: vision-volume
	Volumes string `yaml:"volumes"`

	// PvcName 用于集群之间的多媒体实例产生的附件资源的持久化与同步
	PvcName string `yaml:"pvc_name"`

	// LogDriver 指定多媒体实例使用的日志驱动，一般为 fluentd
	// LogDriver string `yaml:"log_driver"`
	// // FluentdAddr 在 LogDriver == fluentd 的情况下指定了 fluentd server 的服务地址
	// FluentdAddr string `yaml:"fluentd_addr"`
	//
	// // NetworkMode 指定多媒体实例所在的网络
	// NetworkMode string `yaml:"network_mode"`
}

type AgentConfig struct {
	KBConfig DefaultKBConfig `yaml:"kb_config"`
}
type InvokeConfig struct {
	SvcSuffix string `json:"svc_suffix,omitempty" yaml:"svc_suffix"`
}
type DefaultKBConfig struct {
	RerankTopK int32   `yaml:"rerank_top_k"`
	RecallTopK int32   `yaml:"recall_top_k"`
	Threshold  float32 `yaml:"threshold"`
	NamePrefix string  `yaml:"name_prefix"`
}

func (c *ChainDeployConfig) Resources() *serving.Resource {
	return &serving.Resource{
		CpuRequest:    c.CpuRequest,
		CpuLimit:      c.CpuLimit,
		MemoryRequest: c.MemRequest,
		MemoryLimit:   c.MemLimit,
	}
}

func (c *ChainDeployConfig) RestartPolicy() *pb.RestartConfig {
	var restart pb.RestartConfig_Policy
	switch c.Restart {
	case "no":
		restart = pb.RestartConfig_No
	case "on-failure":
		restart = pb.RestartConfig_OnFailure
	case "always":
		restart = pb.RestartConfig_Always
	}
	return &pb.RestartConfig{
		MaxRetry: 5,
		Policy:   restart,
		Backoff: &pb.RestartConfig_Backoff{
			Min:    "1s",
			Max:    "1m",
			Factor: 2.0,
		},
	}
}

// K8sVolumes will convert the volumes of media runner config to a map.
func (c *ChainDeployConfig) K8sVolumes() ([]v1.Volume, []v1.VolumeMount) {
	if c.PvcName == "" {
		stdlog.Errorf("pvc name, which is necessary for mounting volume into media task, has not been specified")
		return nil, nil
	}

	// we need only on volume
	vols := []v1.Volume{
		{
			Name: c.PvcName,
			VolumeSource: v1.VolumeSource{
				PersistentVolumeClaim: &v1.PersistentVolumeClaimVolumeSource{
					ClaimName: c.PvcName,
					ReadOnly:  false,
				},
			},
		},
	}

	vm := c.VolumesMap()
	vms := make([]v1.VolumeMount, 0, len(vm))
	for from, to := range vm {
		vms = append(vms, v1.VolumeMount{
			Name:      c.PvcName,
			ReadOnly:  false,
			MountPath: to,
			SubPath:   from,
		})
	}
	// sort volume mounts before returning
	sort.Slice(vms, func(i, j int) bool {
		return vms[i].Name < vms[j].Name
	})
	return vols, vms
}

// VolumesMap will convert the volumes of media runner config to a map.
func (c *ChainDeployConfig) VolumesMap() map[string]string {
	volumes := make(map[string]string, 0)
	kvs := strings.Split(c.BaseImageConf.Volumes, EnvArraySep)
	for _, kv := range kvs {
		parts := strings.Split(kv, ":")
		if len(parts) != 2 {
			stdlog.Warnf("invalid volume : %s", kv)
			continue
		}
		volumes[strings.TrimSpace(parts[0])] = strings.TrimSpace(parts[1])
	}
	return volumes
}

func (g *GrpcConfig) GetMaxMessageMB() int {
	return g.MaxMessageMB * MB
}

type KnowlhubConfig struct {
	TaskTimeout                  time.Duration `yaml:"task_timeout"`
	HealthzTimeout               time.Duration `yaml:"healthz_timeout"`
	HealthzInterval              time.Duration `yaml:"healthz_interval"`
	DocTaskInterval              time.Duration `yaml:"doc_task_interval"`
	LoadingMaxConcurrency        int           `yaml:"loading_max_concurrency"`
	VectorIndexingBatchSize      int           `yaml:"vector_indexing_batch_size"`
	FullTextIndexingBatchSize    int           `yaml:"fulltext_indexing_batch_size"`
	AugmentQuestionsTemperature  float32       `yaml:"augment_questions_temperature"`   // 知识增强生成提问时的温度
	AugmentDefaultConcurrency    int           `yaml:"augment_default_concurrency"`     // 知识增强调用模型的默认并发度(每个KnowledgeEnhancer)
	VectorEngine                 string        `yaml:"vector_engine"`                   // 内部知识库所使用的向量引擎
	FullTextEngine               string        `yaml:"fulltext_engine"`                 // 内部知识库所使用的全文引擎
	EmbeddingMaxLength           int           `yaml:"embedding_max_length"`            // 向量化支持的最大长度
	DocumentPkUpdate             bool          `yaml:"document_pk_update"`              // 如果是1.x -> 2.x的更新，需要打开一次
	EmbeddingRetryCount          int           `yaml:"embedding_retry_count"`           // emb重试次数
	EmbeddingRetryInterval       time.Duration `yaml:"embedding_retry_interval"`        // emb重试第一次间隔，后续指数上升: 2,4,8,16
	EmbeddingTimeout             time.Duration `yaml:"embedding_timeout"`               // 单次请求embedding的超时
	EmbeddingModelMinConcurrency int           `yaml:"embedding_model_min_concurrency"` // 请求emb模型最小并发
	EmbeddingModelMaxConcurrency int           `yaml:"embedding_model_max_concurrency"` // 请求emb模型最大并发
	RefreshDocumentChunksNum     bool          `yaml:"refresh_document_chunks_num"`     // 是否刷新documents表的num数量
	SyncChunks                   bool          `yaml:"sync_chunks"`                     // 是否同步chunks数据到分表中
}

type AppSvcConfig struct {
	InitInterval       time.Duration `yaml:"init_interval"`
	HealthInterval     time.Duration `yaml:"health_interval"`
	DependencyInterval time.Duration `yaml:"dependency_interval"`
}

type CasConfig struct {
	SvcName string `yaml:"svc_name"`
}
