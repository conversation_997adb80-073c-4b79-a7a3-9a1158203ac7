# 基础配置
edge_id: "local-test-edge"
edge_host: 127.0.0.1
disable_auth: false
database: mysql
storage_root: /sfs
token: Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************.G9bUZ984g-8M-yBuUisrW9HtIK-yP0G-tD4DW9VQ7xAvmyMRaTOJ9DlucZj7KK_ynES1Qa96HZV_Wz5KlUUWtA
server:
  addr: ":30080"
  http_timeout: 3600s

# 公共组件配置, 包含以下组件
# - Mysql     仅云端
# - License
license:
  verifier_path: "/usr/local/bin/verifier"
  licensor_addr: "http://autocv-licensor-service:80"
  check_interval: "5s"

thinger_log:
  # 日志所在默认目录
  path: "/opt/vision/node/.data/store/logs"
  # 后端服务的日志配置
  logger:
    # 日志输出级别
    level: debug
    # 日志是否输出到标准输出
    console: true

kapacitor_config:
  addr: *************:32693
#  addr: *************:9092
  database: thinger
  health_api_path: /api/v1/health
  api_path: /api/v1
  api_port: 1884
  execute_api_port: 31714

mysql:
  username: root
  password: Warp!CV@2022#
  host: **************
  port: 31907
  # host: *************
  # port: 31398
  db_name: applet_backend
  max_idle: 5
  max_conn: 20

etcd:
  addr: http://*************:32480
  dial_timeout: 5

mqtt:
  broker_addr: **************:32581
  qos: 2
  conn_timeout: 10s
  persistent_session: true
  store: data/mqtt/local/

redis:
  addrs: localhost
  database:
  password:
  timeout:
  masterName:

# autocv-cvat-service
prompt_config:
#  host: autocv-cvat-service
  host: *************
#  port: 80
  port: 31030

# autocv-csm-service
csm_config:
  host: *************
  http_port: 32472
  grpc_port: 31562
  #  http_port: 30080
  #  grpc_port: 31080


engine:
  engine_type: "k8s"  #docker or k8s
  #for docker_config
  docker_api_version: "1.32"
  # for k8s_config
  in_cluster: false
  kubeconfig_path: "etc/k3s.yaml"
  image_pull_secret: ""
  api_timeout: "30s"

deploy_config:
  store_path : /data/store
  pvc_mount_cfgs : autocv-sfs-pvc##sfs/store:/applet-engine/data/store
  base_image_conf:
    cpus: 2
    enable_gpu: false
    privileged: false
    mem_limit: 500m
    swap_limit: 500m
    restart: always
    base_image: ***********/aip/applet-engine:dev
    pvc_name: autocv-pvc
    volumes: sfs/store:/data/store


grpc_config:
  max_message_mb: 1024 # MB
  server_host: "localhost"
  server_port: ":31080"


mwh:
#  # 83
  host: *************
  grpc_port: 30684
  # 207
#  host: **************
#  grpc_port: 30434
  default_user: thinger

cvat:
  host: **************
  port: 31411

mlops:
#  port: 31170
#  host: *************
#  port: 30740
#  host: **************
#  port: 30707
  host: **************
  port: 30484
#  gateway: istio-ingressgateway.istio-system
#  gateway_port: 80
#  gateway: *************
#  gateway_port: 31380
  gateway_host: **************
  gateway_port: 31380

hippo_config:
  port: 30387
  username: shiva
  password: shiva
  database: llmops-kb-131
  default_metric_type: IP
  default_index_type: FLAT
  max_chunk_page_size: 10000


scope_config:
  address: http://**************
  port: 30387
  username: shiva
  password: shiva

tkh_config:
  enabled: true
  host: *************:8090
  schema: http
  base_url: ""
  get_token_url: https://*************:28190/studio/api/auth/v1/token/getTestToken
  get_token_body: '{
    "clientId": "app",
    "userName": "admin",
    "password": "Warp1234",
    "clientSecret": "secret"
  }'

doc_svc_config:
  address: http://**************:32356
  load_api:
    url: /api/v1/doc:load
    method: POST
  split_api:
    url: /api/v1/doc:split
    method: POST
  load_split_api:
    url: /api/v1/doc:loadsplit
    method: POST
  loadraw_api:
    url: /api/v1/doc:loadraw
    method: POST
  splitraw_api:
    url: /api/v1/doc:splitraw
    method: POST
  load_chunk_api:
    url: /api/v1/doc:loadchunk
    method: POST
  chunk_api:
    url: /api/v1/doc:chunk
    method: POST
  healthz_api:
    url: /api/v1/healthz
    method: GET
  load_table_api:
    url: /api/v1/doc/table/content
    method: POST
  default_strategy: auto
  default_split_type: recursive
  default_chunk_size: 450
  default_chunk_overlap: 80

default_agent_config:
  kb_config:
    rerank_top_k: 3
    rerank_model_url: dlie://**************:31657
    recall_top_k: 30
    threshold: 0.1
    name_prefix: agent-

knowlhub:
  task_timeout: 1h
  healthz_timeout: 1m
  healthz_interval: 2m
  loading_max_concurrency: 4
  # chunks构建索引时分批的批大小
  hippo_indexing_batch_size: 96
  scope_indexing_batch_size: 100
  # 知识增强生成提问时的温度
  augment_questions_temperature: 1.5
  augment_default_concurrency: 10

api_tool:
  builtin_all_tools_switch: true
  default_proxy:
    schema: PROXY_SCHEME_HTTP
    url: http://*************:3128

doc_engine_config:
  address: **************:6632