# 前端对接指南

## 概述

本文档说明前端如何与Dify插件适配层进行对接，实现插件管理、授权设置和工具调用功能。

## API基础信息

- **Base URL**: `{applet-backend-url}/api/v1/dify-plugins`
- **Content-Type**: `application/json`
- **认证**: 使用现有的LLMOps认证机制

## 核心API接口

### 1. 插件管理

#### 获取插件列表
```javascript
const fetchPlugins = async () => {
  const response = await fetch('/api/v1/dify-plugins/plugins');
  const data = await response.json();
  return data.data.plugins;
};
```

#### 获取插件详情
```javascript
const getPluginDetails = async (pluginId) => {
  const response = await fetch(`/api/v1/dify-plugins/plugins/${pluginId}`);
  const data = await response.json();
  return data.data;
};
```

#### 上传插件（建议直接调用agent-tool-api）
```javascript
const uploadPlugin = async (file) => {
  const formData = new FormData();
  formData.append('file', file);
  
  // 直接调用agent-tool-api
  const response = await fetch('http://agent-tool-api:8080/v1/dify-plugins/upload', {
    method: 'POST',
    body: formData
  });
  
  return await response.json();
};
```

### 2. 插件授权

#### 获取授权表单结构
```javascript
const getAuthForm = async (pluginId) => {
  const response = await fetch(`/api/v1/dify-plugins/plugins/${pluginId}/auth/form`);
  const data = await response.json();
  return data.data.fields;
};
```

#### 设置插件授权
```javascript
const setPluginAuth = async (pluginId, credentials) => {
  const response = await fetch(`/api/v1/dify-plugins/plugins/${pluginId}/auth`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ credentials })
  });
  
  return await response.json();
};
```

### 3. 工具调用

#### 获取可用工具列表
```javascript
const fetchTools = async () => {
  const response = await fetch('/api/v1/dify-plugins/plugins/tools');
  const data = await response.json();
  return data.data.tools;
};
```

#### 调用插件工具
```javascript
const invokePluginTool = async (toolKey, parameters) => {
  const response = await fetch(`/api/v1/dify-plugins/plugins/tools/${toolKey}/invoke`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ parameters })
  });
  
  return await response.json();
};
```

## 前端页面实现建议

### 1. 插件管理页面

**主要功能**：
- 显示已安装插件列表
- 上传新插件
- 查看插件详情
- 卸载插件

**示例代码**：
```vue
<template>
  <div class="plugin-management">
    <div class="header">
      <h2>插件管理</h2>
      <button @click="showUploadDialog = true">上传插件</button>
    </div>
    
    <div class="plugin-list">
      <div v-for="plugin in plugins" :key="plugin.plugin_id" class="plugin-card">
        <div class="plugin-info">
          <h3>{{ plugin.name }}</h3>
          <p>{{ plugin.description }}</p>
          <span class="version">v{{ plugin.version }}</span>
          <span :class="['status', plugin.status]">{{ plugin.status }}</span>
        </div>
        
        <div class="plugin-actions">
          <button v-if="plugin.auth_required && !plugin.has_credentials" 
                  @click="showAuthDialog(plugin)">
            设置授权
          </button>
          <button @click="viewPluginDetails(plugin)">详情</button>
          <button @click="uninstallPlugin(plugin)" class="danger">卸载</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      plugins: [],
      showUploadDialog: false,
      loading: false
    };
  },
  
  async mounted() {
    await this.loadPlugins();
  },
  
  methods: {
    async loadPlugins() {
      try {
        this.loading = true;
        this.plugins = await fetchPlugins();
      } catch (error) {
        this.$message.error('加载插件列表失败');
      } finally {
        this.loading = false;
      }
    },
    
    async showAuthDialog(plugin) {
      // 获取授权表单并显示对话框
      const fields = await getAuthForm(plugin.plugin_id);
      // 显示授权设置对话框
    },
    
    async uninstallPlugin(plugin) {
      // 确认并卸载插件
    }
  }
};
</script>
```

### 2. 插件授权页面

**主要功能**：
- 动态生成授权表单
- 设置和验证凭证
- 管理授权状态

**示例代码**：
```vue
<template>
  <div class="plugin-auth">
    <h3>{{ plugin.name }} - 授权设置</h3>
    
    <form @submit.prevent="submitAuth">
      <div v-for="field in authFields" :key="field.name" class="form-field">
        <label :for="field.name">{{ field.label }}</label>
        <input 
          :id="field.name"
          :type="field.secret ? 'password' : 'text'"
          :placeholder="field.description"
          :required="field.required"
          v-model="credentials[field.name]"
        />
      </div>
      
      <div class="form-actions">
        <button type="submit" :disabled="submitting">
          {{ submitting ? '设置中...' : '设置授权' }}
        </button>
        <button type="button" @click="validateAuth">验证授权</button>
      </div>
    </form>
  </div>
</template>

<script>
export default {
  props: {
    plugin: Object
  },
  
  data() {
    return {
      authFields: [],
      credentials: {},
      submitting: false
    };
  },
  
  async mounted() {
    await this.loadAuthForm();
  },
  
  methods: {
    async loadAuthForm() {
      try {
        this.authFields = await getAuthForm(this.plugin.plugin_id);
        // 初始化credentials对象
        this.authFields.forEach(field => {
          this.$set(this.credentials, field.name, field.default || '');
        });
      } catch (error) {
        this.$message.error('加载授权表单失败');
      }
    },
    
    async submitAuth() {
      try {
        this.submitting = true;
        await setPluginAuth(this.plugin.plugin_id, this.credentials);
        this.$message.success('授权设置成功');
        this.$emit('auth-updated');
      } catch (error) {
        this.$message.error('授权设置失败');
      } finally {
        this.submitting = false;
      }
    },
    
    async validateAuth() {
      // 验证授权
    }
  }
};
</script>
```

### 3. 工具调用页面

**主要功能**：
- 显示可用工具列表
- 动态生成工具参数表单
- 执行工具调用并显示结果

## 错误处理

### 统一错误处理
```javascript
const handleApiCall = async (apiCall) => {
  try {
    const result = await apiCall();
    if (!result.success) {
      throw new Error(result.message);
    }
    return result.data;
  } catch (error) {
    console.error('API调用失败:', error);
    // 显示用户友好的错误信息
    showErrorMessage(error.message);
    throw error;
  }
};
```

### 常见错误码
- `400`: 请求参数错误
- `404`: 资源不存在  
- `500`: 服务器内部错误

## 状态管理建议

### 使用Vuex管理插件状态
```javascript
// store/modules/plugins.js
const state = {
  plugins: [],
  currentPlugin: null,
  tools: [],
  loading: false,
  error: null
};

const mutations = {
  SET_PLUGINS(state, plugins) {
    state.plugins = plugins;
  },
  SET_LOADING(state, loading) {
    state.loading = loading;
  },
  SET_ERROR(state, error) {
    state.error = error;
  }
};

const actions = {
  async fetchPlugins({ commit }) {
    commit('SET_LOADING', true);
    try {
      const plugins = await fetchPlugins();
      commit('SET_PLUGINS', plugins);
    } catch (error) {
      commit('SET_ERROR', error.message);
    } finally {
      commit('SET_LOADING', false);
    }
  }
};
```

## 安全注意事项

1. **凭证保护**: 确保敏感信息在传输过程中加密
2. **输入验证**: 对用户输入进行前端验证
3. **文件上传**: 限制上传文件类型和大小
4. **授权检查**: 确保用户有权限执行相应操作

## 测试建议

### 单元测试
```javascript
describe('插件API测试', () => {
  test('获取插件列表', async () => {
    const plugins = await fetchPlugins();
    expect(Array.isArray(plugins)).toBe(true);
  });
  
  test('设置插件授权', async () => {
    const result = await setPluginAuth('test_plugin', {
      api_key: 'test_key'
    });
    expect(result.success).toBe(true);
  });
});
```

## 部署注意事项

1. **环境配置**: 确保前端能正确访问applet-backend
2. **跨域处理**: 配置CORS或使用代理
3. **错误监控**: 集成错误监控和日志收集
4. **性能优化**: 使用适当的缓存策略

## 联系支持

如有问题，请联系开发团队或查看详细的技术文档。
