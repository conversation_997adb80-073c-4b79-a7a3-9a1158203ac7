[{"id": "code", "author": "Dify", "name": "code", "plugin_id": null, "plugin_unique_identifier": "", "description": {"zh_Hans": "运行一段代码并返回结果。", "en_US": "Run a piece of code and get the result back.", "pt_BR": "Execute um trecho de código e obtenha o resultado de volta.", "ja_JP": "Run a piece of code and get the result back."}, "icon": "/console/api/workspaces/current/tool-provider/builtin/code/icon", "label": {"zh_Hans": "代码解释器", "en_US": "Code Interpreter", "pt_BR": "Interpretador de Código", "ja_JP": "Code Interpreter"}, "type": "builtin", "team_credentials": {}, "is_team_authorization": true, "allow_delete": false, "tools": [], "labels": ["productivity"]}, {"id": "time", "author": "Dify", "name": "time", "plugin_id": null, "plugin_unique_identifier": "", "description": {"zh_Hans": "一个用于获取当前时间的工具。", "en_US": "A tool for getting the current time.", "pt_BR": "A tool for getting the current time.", "ja_JP": "A tool for getting the current time."}, "icon": "/console/api/workspaces/current/tool-provider/builtin/time/icon", "label": {"zh_Hans": "时间", "en_US": "CurrentTime", "pt_BR": "CurrentTime", "ja_JP": "CurrentTime"}, "type": "builtin", "team_credentials": {}, "is_team_authorization": true, "allow_delete": false, "tools": [], "labels": ["utilities"]}, {"id": "webscraper", "author": "Dify", "name": "webscraper", "plugin_id": null, "plugin_unique_identifier": "", "description": {"zh_Hans": "一个用于抓取网页的工具。", "en_US": "Web Scrapper tool kit is used to scrape web", "pt_BR": "Web Scrapper tool kit is used to scrape web", "ja_JP": "Web Scrapper tool kit is used to scrape web"}, "icon": "/console/api/workspaces/current/tool-provider/builtin/webscraper/icon", "label": {"zh_Hans": "网页抓取", "en_US": "WebScraper", "pt_BR": "WebScraper", "ja_JP": "WebScraper"}, "type": "builtin", "team_credentials": {}, "is_team_authorization": true, "allow_delete": false, "tools": [], "labels": ["productivity"]}, {"id": "audio", "author": "h<PERSON><PERSON><PERSON>", "name": "audio", "plugin_id": null, "plugin_unique_identifier": "", "description": {"zh_Hans": "一个用于文本转语音和语音转文本的工具。", "en_US": "A tool for tts and asr.", "pt_BR": "A tool for tts and asr.", "ja_JP": "A tool for tts and asr."}, "icon": "/console/api/workspaces/current/tool-provider/builtin/audio/icon", "label": {"zh_Hans": "Audio", "en_US": "Audio", "pt_BR": "Audio", "ja_JP": "Audio"}, "type": "builtin", "team_credentials": {}, "is_team_authorization": true, "allow_delete": false, "tools": [], "labels": ["utilities"]}, {"id": "langgenius/wecom/wecom", "author": "<PERSON>", "name": "langgenius/wecom/wecom", "plugin_id": "langgenius/wecom", "plugin_unique_identifier": "langgenius/wecom:0.0.3@7a90abb01f25de45d35f26b6bd57dd8d70e0e2c74703274df43d6b2f1c648603", "description": {"zh_Hans": "企业微信群机器人", "en_US": "Wecom group bot", "pt_BR": "Wecom group bot", "ja_JP": "Wecom group bot"}, "icon": "/console/api/workspaces/current/plugin/icon?tenant_id=f24e8fa4-b522-48b2-a5ff-fe639b38d089&filename=0f7de80d305575eb2c8c3c1912209d7cfc5856b66eccb209b73ad769da68578e.png", "label": {"zh_Hans": "企业微信", "en_US": "Wecom", "pt_BR": "Wecom", "ja_JP": "Wecom"}, "type": "builtin", "team_credentials": {}, "is_team_authorization": true, "allow_delete": false, "tools": [], "labels": ["social"]}, {"id": "langgenius/wikipedia/wikipedia", "author": "lang<PERSON><PERSON>", "name": "langgenius/wikipedia/wikipedia", "plugin_id": "langgenius/wikipedia", "plugin_unique_identifier": "langgenius/wikipedia:0.0.3@63972698f033252b19a23a203f0beb6c3af16c6e8b0b886f001d902165a3da0c", "description": {"zh_Hans": "维基百科是一个由全世界的志愿者创建和编辑的免费在线百科全书。", "en_US": "Wikipedia is a free online encyclopedia, created and edited by volunteers around the world.", "pt_BR": "Wikipedia is a free online encyclopedia, created and edited by volunteers around the world.", "ja_JP": "Wikipedia is a free online encyclopedia, created and edited by volunteers around the world."}, "icon": "/console/api/workspaces/current/plugin/icon?tenant_id=f24e8fa4-b522-48b2-a5ff-fe639b38d089&filename=a83ac9eac258f6710e51050e914e9b97d978fcbfc2b53923f4f320c8eef44424.svg", "label": {"zh_Hans": "维基百科", "en_US": "Wikipedia", "pt_BR": "Wikipedia", "ja_JP": "Wikipedia"}, "type": "builtin", "team_credentials": {}, "is_team_authorization": true, "allow_delete": false, "tools": [], "labels": ["social"]}, {"id": "lang<PERSON><PERSON>/yahoo/yahoo", "author": "lang<PERSON><PERSON>", "name": "lang<PERSON><PERSON>/yahoo/yahoo", "plugin_id": "langgenius/yahoo", "plugin_unique_identifier": "langgenius/yahoo:0.0.5@b0bf4aa3062c9b41ce3eaac8e4ea1987f7daef614702b4f3a11755629b35bb28", "description": {"zh_Hans": "雅虎财经，获取并整理出最新的新闻、股票报价等一切你想要的财经信息。", "en_US": "Finance, and Yahoo! get the latest news, stock quotes, and interactive chart with Yahoo!", "pt_BR": "Finance, and Yahoo! get the latest news, stock quotes, and interactive chart with Yahoo!", "ja_JP": "Finance, and Yahoo! get the latest news, stock quotes, and interactive chart with Yahoo!"}, "icon": "/console/api/workspaces/current/plugin/icon?tenant_id=f24e8fa4-b522-48b2-a5ff-fe639b38d089&filename=3bda2714e412b23883059aed5cfc5e882f357fd1ac563188b37eeeba400c39c9.png", "label": {"zh_Hans": "雅虎财经", "en_US": "YahooFinance", "pt_BR": "YahooFinance", "ja_JP": "YahooFinance"}, "type": "builtin", "team_credentials": {}, "is_team_authorization": true, "allow_delete": false, "tools": [], "labels": ["business", "finance"]}, {"id": "langgenius/searxng/searxng", "author": "Junytang", "name": "langgenius/searxng/searxng", "plugin_id": "langgenius/searxng", "plugin_unique_identifier": "langgenius/searxng:0.0.7@fce43eac17ce659811cfa35bef8858d06f78bb832eff5d0317dab34982991eca", "description": {"zh_Hans": "开源免费的互联网元搜索引擎", "en_US": "A free internet metasearch engine.", "pt_BR": "A free internet metasearch engine.", "ja_JP": "A free internet metasearch engine."}, "icon": "/console/api/workspaces/current/plugin/icon?tenant_id=f24e8fa4-b522-48b2-a5ff-fe639b38d089&filename=ed0f6d711dc2f632e3ead5e98389e06b78450e0675cb150216fa59650b191d3d.svg", "label": {"zh_Hans": "SearXNG", "en_US": "SearXNG", "pt_BR": "SearXNG", "ja_JP": "SearXNG"}, "type": "builtin", "team_credentials": {}, "is_team_authorization": false, "allow_delete": true, "tools": [], "labels": ["search", "productivity"]}, {"id": "langgenius/regex/regex", "author": "<PERSON><PERSON><PERSON>", "name": "langgenius/regex/regex", "plugin_id": "langgenius/regex", "plugin_unique_identifier": "langgenius/regex:0.0.3@257eaab07b70ab1f77a881b870eefee93fc8fd0dd13350077410264f31695039", "description": {"zh_Hans": "一个用于正则表达式内容提取的工具。", "en_US": "A tool for regex extraction.", "pt_BR": "A tool for regex extraction.", "ja_JP": "A tool for regex extraction."}, "icon": "/console/api/workspaces/current/plugin/icon?tenant_id=f24e8fa4-b522-48b2-a5ff-fe639b38d089&filename=d436a70a8224c47ba88e07d7e5e85aacef607176b5d4004dd6237fc425e77098.svg", "label": {"zh_Hans": "正则表达式提取", "en_US": "Regex", "pt_BR": "Regex", "ja_JP": "Regex"}, "type": "builtin", "team_credentials": {}, "is_team_authorization": true, "allow_delete": false, "tools": [], "labels": ["utilities", "productivity"]}, {"id": "baobaobao/weather/weather", "author": "baobaobao", "name": "baobaobao/weather/weather", "plugin_id": "baobaobao/weather", "plugin_unique_identifier": "baobaobao/weather:0.0.3@18136b085ec7e22ad6739c0fdf4c92e1ae8d191566f8a3581f5a149aa8a6d28e", "description": {"zh_Hans": "天气查询是一个用于获取实时天气数据和预报信息的服务接口。该API提供国内范围内的天气状况、预报和相关气象数据。", "en_US": "weather", "pt_BR": "weather", "ja_JP": "weather"}, "icon": "/console/api/workspaces/current/plugin/icon?tenant_id=f24e8fa4-b522-48b2-a5ff-fe639b38d089&filename=09ee44c34c4c8cee99ed7347a5cf9cef032cf53251a362f088104f76f56ae51c.svg", "label": {"zh_Hans": "天气查询", "en_US": "weather", "pt_BR": "weather", "ja_JP": "weather"}, "type": "builtin", "team_credentials": {}, "is_team_authorization": true, "allow_delete": true, "tools": [], "labels": []}, {"id": "langgenius/arxiv/arxiv", "author": "yash_parmar", "name": "langgenius/arxiv/arxiv", "plugin_id": "langgenius/arxiv", "plugin_unique_identifier": "langgenius/arxiv:0.0.2@906eec47ee2ae429a628ad0300173ad962d24930883814e36c1b0c2fc09bdfdf", "description": {"zh_Hans": "访问各个研究领域大量科学论文和文章的存储库。", "en_US": "Access to a vast repository of scientific papers and articles in various fields of research.", "pt_BR": "Access to a vast repository of scientific papers and articles in various fields of research.", "ja_JP": "Access to a vast repository of scientific papers and articles in various fields of research."}, "icon": "/console/api/workspaces/current/plugin/icon?tenant_id=f24e8fa4-b522-48b2-a5ff-fe639b38d089&filename=0d9a77329fb97c838ac700ed9065242a11b88c19edb4ba084205d320a3579bdb.svg", "label": {"zh_Hans": "ArXiv", "en_US": "ArXiv", "pt_BR": "ArXiv", "ja_JP": "ArXiv"}, "type": "builtin", "team_credentials": {}, "is_team_authorization": true, "allow_delete": false, "tools": [], "labels": ["search"]}, {"id": "langgenius/e2b/e2b", "author": "lang<PERSON><PERSON>", "name": "langgenius/e2b/e2b", "plugin_id": "langgenius/e2b", "plugin_unique_identifier": "langgenius/e2b:0.0.1@62201c9d53574b721e5b0649cd4e73c57808b1e9484ad55b2f22c60def0edaba", "description": {"zh_Hans": "Tool for integrate with e2b.dev.", "en_US": "Tool for integrate with e2b.dev.", "pt_BR": "Tool for integrate with e2b.dev.", "ja_JP": "Tool for integrate with e2b.dev."}, "icon": "/console/api/workspaces/current/plugin/icon?tenant_id=f24e8fa4-b522-48b2-a5ff-fe639b38d089&filename=972f9bc0261570a91b18fcdcfdf31d3e65b8db3c02a76ccf61f2a101282a0744.png", "label": {"zh_Hans": "E2B", "en_US": "E2B", "pt_BR": "E2B", "ja_JP": "E2B"}, "type": "builtin", "team_credentials": {}, "is_team_authorization": false, "allow_delete": true, "tools": [], "labels": []}, {"id": "langgenius/email/email", "author": "wakaka6", "name": "langgenius/email/email", "plugin_id": "langgenius/email", "plugin_unique_identifier": "langgenius/email:0.0.7@6c3856490275f3440619fabfdc54f3bd29bad8959e29996452277b9c3b0c5e9b", "description": {"zh_Hans": "通过smtp协议发送电子邮件", "en_US": "send email through smtp protocol", "pt_BR": "send email through smtp protocol", "ja_JP": "send email through smtp protocol"}, "icon": "/console/api/workspaces/current/plugin/icon?tenant_id=f24e8fa4-b522-48b2-a5ff-fe639b38d089&filename=8f2d1b31d65a6085e8a845cb9bb7ec37399576001e6065fceaf344bffbeb393f.svg", "label": {"zh_Hans": "电子邮件", "en_US": "email", "pt_BR": "email", "ja_JP": "email"}, "type": "builtin", "team_credentials": {}, "is_team_authorization": true, "allow_delete": true, "tools": [], "labels": ["utilities"]}, {"id": "lang<PERSON><PERSON>/dingtalk/dingtalk", "author": "Bowen Liang & LiuHao", "name": "lang<PERSON><PERSON>/dingtalk/dingtalk", "plugin_id": "lang<PERSON>ius/dingtalk", "plugin_unique_identifier": "langgenius/dingtalk:0.0.4@1a167a005bd3509142f45f7a771f887c75437ea367840dee9760782a1a1ef082", "description": {"zh_Hans": "钉钉群机器人", "en_US": "DingTalk group robot", "pt_BR": "DingTalk group robot", "ja_JP": "DingTalk group robot"}, "icon": "/console/api/workspaces/current/plugin/icon?tenant_id=f24e8fa4-b522-48b2-a5ff-fe639b38d089&filename=a21f02d16b0be15978b070528f938f57674668be2965c6604ed644b66e4d857a.svg", "label": {"zh_Hans": "钉钉", "en_US": "DingTalk", "pt_BR": "DingTalk", "ja_JP": "DingTalk"}, "type": "builtin", "team_credentials": {}, "is_team_authorization": true, "allow_delete": false, "tools": [], "labels": ["social", "productivity"]}, {"id": "langgenius/firecrawl/firecrawl", "author": "<PERSON><PERSON><PERSON>", "name": "langgenius/firecrawl/firecrawl", "plugin_id": "langgenius/firecrawl", "plugin_unique_identifier": "langgenius/firecrawl:0.0.3@02f037a6e82c44103d3604456f8e57b5516464183344f32d74eec3f82d6036b0", "description": {"zh_Hans": "Firecrawl API 集成，用于网页爬取和数据抓取。", "en_US": "Firecrawl API integration for web crawling and scraping.", "pt_BR": "Firecrawl API integration for web crawling and scraping.", "ja_JP": "Firecrawl API integration for web crawling and scraping."}, "icon": "/console/api/workspaces/current/plugin/icon?tenant_id=f24e8fa4-b522-48b2-a5ff-fe639b38d089&filename=48e81335e9c5fb6275f29e7a019e774ece91f1f63daf4b3d375f03accce99b62.svg", "label": {"zh_Hans": "Firecrawl", "en_US": "Firecrawl", "pt_BR": "Firecrawl", "ja_JP": "Firecrawl"}, "type": "builtin", "team_credentials": {}, "is_team_authorization": true, "allow_delete": true, "tools": [], "labels": ["search", "utilities"]}, {"id": "langgenius/google/google", "author": "Dify", "name": "langgenius/google/google", "plugin_id": "langgenius/google", "plugin_unique_identifier": "langgenius/google:0.0.9@d360bbc433f39be1b11909cb9c32e6be4a17ea06af083f9e1c7613bb802bf517", "description": {"zh_Hans": "GoogleSearch", "en_US": "Google", "pt_BR": "Google", "ja_JP": "Google"}, "icon": "/console/api/workspaces/current/plugin/icon?tenant_id=f24e8fa4-b522-48b2-a5ff-fe639b38d089&filename=1c5871163478957bac64c3fe33d72d003f767497d921c74b742aad27a8344a74.svg", "label": {"zh_Hans": "Google", "en_US": "Google", "pt_BR": "Google", "ja_JP": "Google"}, "type": "builtin", "team_credentials": {}, "is_team_authorization": true, "allow_delete": true, "tools": [], "labels": ["search"]}]