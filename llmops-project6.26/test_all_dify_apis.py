#!/usr/bin/env python3
"""
测试所有Dify插件API端点
"""

import requests
import json
import sys

def test_dify_plugin_apis():
    """测试所有Dify插件API端点"""
    
    # 配置
    base_url = "http://172.16.201.93:30080"
    auth_token = "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5MDk5OTEsImlhdCI6MTY1NjMwOTk5MSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.G9bUZ984g-8M-yBuUisrW9HtIK-yP0G-tD4DW9VQ7xAvmyMRaTOJ9DlucZj7KK_ynES1Qa96HZV_Wz5KlUUWtA"
    
    headers = {
        'Authorization': auth_token,
        'Content-Type': 'application/json'
    }
    
    print("🧪 测试所有Dify插件API端点")
    print("=" * 60)
    print(f"Base URL: {base_url}")
    print(f"Auth Token: {auth_token[:50]}...")
    
    # 定义所有API端点
    api_endpoints = [
        {
            'name': '获取插件列表',
            'method': 'GET',
            'url': f"{base_url}/api/v1/app/dify-plugins/plugins",
            'expected_status': [200, 500]  # 可能返回500如果agent-tool-api未运行
        },
        {
            'name': '获取插件详情',
            'method': 'GET', 
            'url': f"{base_url}/api/v1/app/dify-plugins/plugins/test_plugin",
            'expected_status': [200, 404, 500]
        },
        {
            'name': '获取插件授权表单',
            'method': 'GET',
            'url': f"{base_url}/api/v1/app/dify-plugins/plugins/test_plugin/auth/form",
            'expected_status': [200, 404, 500]
        },
        {
            'name': '设置插件授权',
            'method': 'POST',
            'url': f"{base_url}/api/v1/app/dify-plugins/plugins/test_plugin/auth",
            'data': {"credentials": {"api_key": "test_key"}},
            'expected_status': [200, 404, 500]
        },
        {
            'name': '验证插件授权',
            'method': 'POST',
            'url': f"{base_url}/api/v1/app/dify-plugins/plugins/test_plugin/auth/validate",
            'expected_status': [200, 404, 500]
        },
        {
            'name': '移除插件授权',
            'method': 'DELETE',
            'url': f"{base_url}/api/v1/app/dify-plugins/plugins/test_plugin/auth",
            'expected_status': [200, 404, 500]
        },
        {
            'name': '卸载插件',
            'method': 'DELETE',
            'url': f"{base_url}/api/v1/app/dify-plugins/plugins/test_plugin",
            'expected_status': [200, 404, 500]
        },
        {
            'name': '获取插件工具列表',
            'method': 'GET',
            'url': f"{base_url}/api/v1/app/dify-plugins/plugins/tools",
            'expected_status': [200, 500]
        },
        {
            'name': '调用插件工具',
            'method': 'POST',
            'url': f"{base_url}/api/v1/app/dify-plugins/plugins/tools/test_tool/invoke",
            'data': {"parameters": {"param1": "value1"}},
            'expected_status': [200, 404, 500]
        },
        {
            'name': '获取工具Schema',
            'method': 'GET',
            'url': f"{base_url}/api/v1/app/dify-plugins/plugins/tools/schema",
            'expected_status': [200, 500]
        },
        {
            'name': '获取系统状态',
            'method': 'GET',
            'url': f"{base_url}/api/v1/app/dify-plugins/plugins/status",
            'expected_status': [200, 500]
        },
        {
            'name': '刷新插件',
            'method': 'POST',
            'url': f"{base_url}/api/v1/app/dify-plugins/plugins/refresh",
            'expected_status': [200, 500]
        }
    ]
    
    results = []
    
    for endpoint in api_endpoints:
        print(f"\n📡 测试: {endpoint['name']}")
        print(f"   {endpoint['method']} {endpoint['url']}")
        
        try:
            if endpoint['method'] == 'GET':
                response = requests.get(endpoint['url'], headers=headers, timeout=10)
            elif endpoint['method'] == 'POST':
                data = endpoint.get('data', {})
                response = requests.post(endpoint['url'], headers=headers, json=data, timeout=10)
            elif endpoint['method'] == 'DELETE':
                response = requests.delete(endpoint['url'], headers=headers, timeout=10)
            else:
                print(f"   ❌ 不支持的HTTP方法: {endpoint['method']}")
                continue
            
            status_code = response.status_code
            print(f"   状态码: {status_code}")
            
            # 检查状态码是否符合预期
            if status_code in endpoint['expected_status']:
                if status_code == 404:
                    print(f"   ❌ 404 - API端点不存在或路径错误")
                    results.append({'name': endpoint['name'], 'status': 'FAILED', 'reason': '404 Not Found'})
                elif status_code == 500:
                    print(f"   ⚠️  500 - 服务器内部错误（可能是agent-tool-api未运行）")
                    try:
                        error_detail = response.json().get('msg', response.text[:100])
                        print(f"   错误详情: {error_detail}")
                    except:
                        print(f"   错误详情: {response.text[:100]}")
                    results.append({'name': endpoint['name'], 'status': 'PARTIAL', 'reason': '500 Internal Error'})
                else:
                    print(f"   ✅ API端点存在且可访问")
                    try:
                        response_data = response.json()
                        print(f"   响应: {json.dumps(response_data, ensure_ascii=False)[:100]}...")
                    except:
                        print(f"   响应: {response.text[:100]}...")
                    results.append({'name': endpoint['name'], 'status': 'SUCCESS', 'reason': 'OK'})
            else:
                print(f"   ❌ 意外的状态码: {status_code}")
                print(f"   响应: {response.text[:200]}")
                results.append({'name': endpoint['name'], 'status': 'FAILED', 'reason': f'Unexpected status {status_code}'})
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 连接失败 - 无法连接到服务器")
            results.append({'name': endpoint['name'], 'status': 'FAILED', 'reason': 'Connection Error'})
        except requests.exceptions.Timeout:
            print(f"   ❌ 请求超时")
            results.append({'name': endpoint['name'], 'status': 'FAILED', 'reason': 'Timeout'})
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")
            results.append({'name': endpoint['name'], 'status': 'FAILED', 'reason': str(e)})
    
    # 输出总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    success_count = len([r for r in results if r['status'] == 'SUCCESS'])
    partial_count = len([r for r in results if r['status'] == 'PARTIAL'])
    failed_count = len([r for r in results if r['status'] == 'FAILED'])
    
    print(f"✅ 成功: {success_count}")
    print(f"⚠️  部分成功: {partial_count}")
    print(f"❌ 失败: {failed_count}")
    
    if failed_count > 0:
        print(f"\n❌ 失败的API:")
        for result in results:
            if result['status'] == 'FAILED':
                print(f"   - {result['name']}: {result['reason']}")
    
    if partial_count > 0:
        print(f"\n⚠️  部分成功的API (可能需要启动agent-tool-api):")
        for result in results:
            if result['status'] == 'PARTIAL':
                print(f"   - {result['name']}: {result['reason']}")
    
    print(f"\n🎯 结论:")
    if failed_count == 0:
        print("✅ 所有API端点都已正确实现和注册！")
        if partial_count > 0:
            print("💡 部分API返回500错误，请确保agent-tool-api服务正在运行")
        return True
    else:
        print("❌ 部分API端点存在问题，需要检查路由注册或实现")
        return False

def main():
    """主函数"""
    print("开始测试所有Dify插件API端点...")
    
    success = test_dify_plugin_apis()
    
    print(f"\n📋 正确的API路径前缀: /api/v1/app/dify-plugins/")
    print(f"🔧 如果遇到404错误，请检查URL路径是否正确")
    print(f"🔧 如果遇到500错误，请确保agent-tool-api服务正在运行")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
