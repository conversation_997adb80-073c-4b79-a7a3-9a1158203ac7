#!/usr/bin/env python3
"""
调试插件数据结构
"""

import requests
import json
import sys

def debug_plugin_structure():
    """调试插件数据结构"""
    print("🔍 调试插件数据结构...")
    
    auth_token = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.HWPgwNIp0ux1ii9AAcM7CSxKXiTQ3I2r63uhd8FWuxI"
    
    try:
        url = "http://************/console/api/workspaces/current/plugin/list"
        headers = {
            "Authorization": auth_token,
            "Content-Type": "application/json"
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            data = response.json()
            plugins = data.get('plugins', [])
            print(f"✅ 获取到 {len(plugins)} 个插件")
            
            # 分析前几个插件的结构
            for i, plugin in enumerate(plugins[:3]):
                print(f"\n📋 插件 {i+1}: {plugin.get('name', 'Unknown')}")
                print(f"unique_identifier: {plugin.get('unique_identifier', 'None')}")
                print(f"顶级keys: {list(plugin.keys())}")
                
                declaration = plugin.get('declaration', {})
                print(f"declaration keys: {list(declaration.keys())}")
                
                # 检查tool结构
                tool_info = declaration.get('tool', {})
                if tool_info:
                    print(f"tool keys: {list(tool_info.keys())}")
                    
                    # 检查identity
                    identity = tool_info.get('identity', {})
                    if identity:
                        print(f"tool identity: {identity}")
                    
                    # 检查tools数组
                    tools = tool_info.get('tools', [])
                    print(f"tools数量: {len(tools)}")
                    
                    for j, tool in enumerate(tools[:2]):  # 只看前2个tool
                        print(f"  tool {j+1} keys: {list(tool.keys())}")
                        tool_identity = tool.get('identity', {})
                        if tool_identity:
                            print(f"  tool {j+1} identity: {tool_identity}")
                
                # 检查其他可能的结构
                for key in declaration.keys():
                    if key != 'tool':
                        print(f"{key}: {type(declaration[key])}")
                        if isinstance(declaration[key], dict):
                            print(f"  {key} keys: {list(declaration[key].keys())}")
                
                print("-" * 50)
            
            # 保存完整数据到文件以便详细分析
            with open('plugin_structure_debug.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"\n💾 完整数据已保存到 plugin_structure_debug.json")
            
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def debug_tool_providers():
    """调试工具提供者结构"""
    print("\n🔧 调试工具提供者结构...")
    
    auth_token = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.HWPgwNIp0ux1ii9AAcM7CSxKXiTQ3I2r63uhd8FWuxI"
    
    try:
        url = "http://************/console/api/workspaces/current/tool-providers"
        headers = {
            "Authorization": auth_token,
            "Content-Type": "application/json"
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            providers = response.json()
            print(f"✅ 获取到 {len(providers)} 个工具提供者")
            
            # 分析前几个提供者的结构
            for i, provider in enumerate(providers[:5]):
                name = provider.get('name', 'Unknown')
                is_auth = provider.get('is_team_authorization', False)
                provider_type = provider.get('type', 'unknown')
                
                print(f"- {name}: {provider_type}, 授权: {is_auth}")
            
            # 保存完整数据到文件
            with open('tool_providers_debug.json', 'w', encoding='utf-8') as f:
                json.dump(providers, f, indent=2, ensure_ascii=False)
            print(f"\n💾 工具提供者数据已保存到 tool_providers_debug.json")
            
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def main():
    """主函数"""
    print("🚀 开始调试插件数据结构")
    print("=" * 60)
    
    debug_plugin_structure()
    debug_tool_providers()
    
    print("\n" + "=" * 60)
    print("🎉 调试完成，请查看生成的JSON文件了解数据结构")

if __name__ == "__main__":
    main()
