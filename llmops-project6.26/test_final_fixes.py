#!/usr/bin/env python3
"""
测试最终修复效果
"""

import requests
import json
import sys
import urllib.parse

def test_final_fixes():
    """测试最终修复效果"""
    
    # 配置
    base_url = "http://172.16.201.93:30080"
    auth_token = "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5MDk5OTEsImlhdCI6MTY1NjMwOTk5MSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.G9bUZ984g-8M-yBuUisrW9HtIK-yP0G-tD4DW9VQ7xAvmyMRaTOJ9DlucZj7KK_ynES1Qa96HZV_Wz5KlUUWtA"
    
    headers = {
        'Authorization': auth_token,
        'Content-Type': 'application/json'
    }
    
    print("🔧 测试最终修复效果")
    print("=" * 60)
    
    # 1. 获取插件列表
    print("\n📋 步骤1: 获取插件列表")
    plugins_data = []
    
    try:
        response = requests.get(f"{base_url}/api/v1/app/dify-plugins/plugins", headers=headers, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            plugins = data.get('plugins', [])
            print(f"✅ 成功获取插件列表，共 {len(plugins)} 个插件")
            
            if plugins:
                print("\n插件列表:")
                for i, plugin in enumerate(plugins):
                    plugin_id = plugin.get('plugin_id', 'Unknown')
                    name = plugin.get('name', 'Unknown')
                    source = plugin.get('source', 'Unknown')
                    
                    plugins_data.append({
                        'plugin_id': plugin_id,
                        'name': name,
                        'source': source
                    })
                    
                    print(f"  {i+1}. {name} ({plugin_id}) - {source}")
                    
                    if i >= 4:  # 只显示前5个插件
                        if len(plugins) > 5:
                            print(f"     ... 还有 {len(plugins) - 5} 个插件")
                        break
            else:
                print("⚠️  插件列表为空")
        else:
            print(f"❌ 获取插件列表失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False
    
    # 2. 测试插件详情获取（修复JSON解析错误）
    print(f"\n📄 步骤2: 测试插件详情获取")
    
    test_plugin_ids = ['google', 'firecrawl', 'langgenius/firecrawl']
    
    for test_id in test_plugin_ids:
        print(f"\n测试插件ID: '{test_id}'")
        
        try:
            encoded_id = urllib.parse.quote(test_id, safe='')
            detail_response = requests.get(
                f"{base_url}/api/v1/app/dify-plugins/plugins/{encoded_id}", 
                headers=headers, 
                timeout=15
            )
            
            print(f"  状态码: {detail_response.status_code}")
            
            if detail_response.status_code == 200:
                detail_data = detail_response.json()
                print(f"  ✅ 成功获取插件详情")
                print(f"  名称: {detail_data.get('name', 'Unknown')}")
                print(f"  版本: {detail_data.get('version', 'Unknown')}")
                print(f"  权限类型: {type(detail_data.get('permissions', 'None'))}")
                
                # 检查permissions字段是否正确解析
                permissions = detail_data.get('permissions')
                if permissions is not None:
                    print(f"  权限内容: {str(permissions)[:100]}...")
                else:
                    print(f"  权限内容: None")
                    
            elif detail_response.status_code == 404:
                print(f"  ❌ 插件不存在 (404)")
            elif detail_response.status_code == 500:
                error_text = detail_response.text
                if "cannot unmarshal array" in error_text:
                    print(f"  ❌ JSON解析错误（未修复）: {error_text[:100]}...")
                else:
                    print(f"  ❌ 其他500错误: {error_text[:100]}...")
            else:
                print(f"  ❌ 其他错误: {detail_response.text[:100]}...")
                
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")
    
    # 3. 测试授权表单获取（修复删除插件后仍返回表单的问题）
    print(f"\n🔐 步骤3: 测试授权表单获取")
    
    # 测试存在的插件
    if plugins_data:
        test_plugin = plugins_data[0]
        plugin_id = test_plugin['plugin_id']
        
        print(f"\n测试存在的插件: {plugin_id}")
        try:
            encoded_id = urllib.parse.quote(plugin_id, safe='')
            auth_response = requests.get(
                f"{base_url}/api/v1/app/dify-plugins/plugins/{encoded_id}/auth/form", 
                headers=headers, 
                timeout=15
            )
            
            print(f"  状态码: {auth_response.status_code}")
            
            if auth_response.status_code == 200:
                auth_data = auth_response.json()
                fields = auth_data.get('fields', [])
                print(f"  ✅ 成功获取授权表单，字段数量: {len(fields)}")
                
                if fields:
                    print("  授权字段:")
                    for field in fields[:3]:  # 只显示前3个字段
                        print(f"    - {field.get('name', 'Unknown')}: {field.get('label', 'No label')}")
                else:
                    print("  ⚠️  授权表单为空")
            else:
                print(f"  ❌ 获取授权表单失败: {auth_response.text[:100]}...")
                
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")
    
    # 测试不存在的插件
    print(f"\n测试不存在的插件: 'nonexistent_plugin'")
    try:
        auth_response = requests.get(
            f"{base_url}/api/v1/app/dify-plugins/plugins/nonexistent_plugin/auth/form", 
            headers=headers, 
            timeout=15
        )
        
        print(f"  状态码: {auth_response.status_code}")
        
        if auth_response.status_code == 200:
            auth_data = auth_response.json()
            fields = auth_data.get('fields', [])
            if len(fields) == 0:
                print(f"  ✅ 正确返回空授权表单")
            else:
                print(f"  ❌ 不应该返回授权字段: {len(fields)} 个字段")
        elif auth_response.status_code == 404:
            print(f"  ✅ 正确返回404 - 插件不存在")
        else:
            print(f"  ⚠️  其他响应: {auth_response.text[:100]}...")
            
    except Exception as e:
        print(f"  ❌ 请求异常: {e}")
    
    # 4. 测试特定的问题场景
    print(f"\n🎯 步骤4: 测试特定问题场景")
    
    # 测试firecrawl授权表单
    print(f"\n测试firecrawl授权表单:")
    test_ids = ['firecrawl', 'langgenius%2Ffirecrawl', 'Firecrawl']
    
    for test_id in test_ids:
        print(f"  测试ID: {test_id}")
        try:
            auth_response = requests.get(
                f"{base_url}/api/v1/app/dify-plugins/plugins/{test_id}/auth/form", 
                headers=headers, 
                timeout=10
            )
            
            print(f"    状态码: {auth_response.status_code}")
            
            if auth_response.status_code == 200:
                auth_data = auth_response.json()
                fields = auth_data.get('fields', [])
                print(f"    字段数量: {len(fields)}")
                
                if fields:
                    for field in fields[:2]:  # 只显示前2个字段
                        print(f"      - {field.get('name', 'Unknown')}")
            else:
                print(f"    错误: {auth_response.text[:50]}...")
                
        except Exception as e:
            print(f"    异常: {e}")
    
    print(f"\n" + "=" * 60)
    print("📊 修复内容总结:")
    print("✅ 修复了JSON解析错误 - permissions字段类型问题")
    print("✅ 修复了授权表单获取逻辑 - 从Dify服务器获取")
    print("✅ 修复了删除插件后仍返回表单的问题")
    print("✅ 增强了插件ID匹配逻辑")
    
    print(f"\n🎯 预期效果:")
    print("1. 插件详情不再出现JSON解析错误")
    print("2. 授权表单能正确获取或返回空")
    print("3. 删除的插件返回'插件不存在'")
    print("4. 支持多种插件ID格式")

def main():
    """主函数"""
    print("开始测试最终修复效果...")
    
    test_final_fixes()
    
    print(f"\n💡 如果仍有问题，请检查:")
    print("1. agent-tool-api服务是否已重启")
    print("2. applet-backend服务是否已重启")
    print("3. Dify服务器连接是否正常")
    print("4. 插件是否真的存在于Dify中")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
