#!/usr/bin/env python3
"""
测试Dify API修复
"""

import requests
import json
import sys

def test_dify_api_endpoints():
    """测试Dify API端点是否正确"""
    
    # 从配置文件读取配置
    with open('agent-tool-api/config.json', 'r') as f:
        config = json.load(f)
    
    base_url = config['dify_base_url']
    auth_token = config['dify_auth_token']
    
    print("🧪 测试Dify API端点修复")
    print("=" * 50)
    print(f"Base URL: {base_url}")
    print(f"Auth Token: {auth_token[:50]}...")
    
    # 测试端点
    endpoints_to_test = [
        {
            'name': '上传插件端点',
            'url': f"{base_url}/console/api/workspaces/current/plugin/upload/pkg",
            'method': 'POST',
            'expected_error': 'multipart'  # 期望因为没有文件而报错
        },
        {
            'name': '安装插件端点', 
            'url': f"{base_url}/console/api/workspaces/current/plugin/install/pkg",
            'method': 'POST',
            'expected_error': 'plugin_unique_identifiers'  # 期望因为没有参数而报错
        }
    ]
    
    headers = {
        'Authorization': auth_token,
        'Content-Type': 'application/json'
    }
    
    all_passed = True
    
    for endpoint in endpoints_to_test:
        print(f"\n📡 测试 {endpoint['name']}...")
        print(f"URL: {endpoint['url']}")
        
        try:
            if endpoint['method'] == 'POST':
                response = requests.post(endpoint['url'], headers=headers, json={}, timeout=10)
            else:
                response = requests.get(endpoint['url'], headers=headers, timeout=10)
            
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text[:200]}...")
            
            # 检查是否是预期的错误
            if endpoint['expected_error'] in response.text.lower():
                print(f"✅ {endpoint['name']} - 端点可访问，返回预期错误")
            elif response.status_code == 404:
                print(f"❌ {endpoint['name']} - 端点不存在 (404)")
                all_passed = False
            elif response.status_code == 401:
                print(f"❌ {endpoint['name']} - 认证失败 (401)")
                all_passed = False
            elif response.status_code == 403:
                print(f"❌ {endpoint['name']} - 权限不足 (403)")
                all_passed = False
            else:
                print(f"⚠️  {endpoint['name']} - 未知响应，需要进一步检查")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {endpoint['name']} - 无法连接到服务器")
            all_passed = False
        except requests.exceptions.Timeout:
            print(f"❌ {endpoint['name']} - 请求超时")
            all_passed = False
        except Exception as e:
            print(f"❌ {endpoint['name']} - 请求失败: {e}")
            all_passed = False
    
    return all_passed

def test_agent_tool_api():
    """测试agent-tool-api是否能正常工作"""
    print(f"\n🔧 测试agent-tool-api...")
    
    try:
        response = requests.get('http://localhost:8080/v1/dify-plugins/status', timeout=5)
        if response.status_code == 200:
            print("✅ agent-tool-api运行正常")
            return True
        else:
            print(f"❌ agent-tool-api状态异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接agent-tool-api: {e}")
        return False

def main():
    """主函数"""
    print("开始测试Dify API修复...")
    
    # 测试agent-tool-api
    agent_api_ok = test_agent_tool_api()
    
    # 测试Dify API端点
    dify_api_ok = test_dify_api_endpoints()
    
    print("\n" + "=" * 50)
    print("📋 修复内容总结:")
    print("- ✅ 修复了Dify API端点URL")
    print("  - 上传: /console/api/workspaces/current/plugin/upload/pkg")
    print("  - 安装: /console/api/workspaces/current/plugin/install/pkg")
    print("- ✅ 修复了文件字段名称 (pkg)")
    print("- ✅ 修复了base_url配置 (移除:5002端口)")
    print("- ✅ 简化了安装请求payload")
    
    if agent_api_ok and dify_api_ok:
        print("\n🎉 所有测试通过！修复成功")
        print("\n🚀 现在可以尝试上传插件了:")
        print("curl --location 'http://172.16.201.93:30080/api/v1/app/dify-plugins/plugins/upload' \\")
        print("--header 'Authorization: Bearer your-token' \\")
        print("--form 'pkg=@\"your-plugin.difypkg\"'")
        return 0
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        if not agent_api_ok:
            print("- agent-tool-api未正常运行")
        if not dify_api_ok:
            print("- Dify API端点存在问题")
        return 1

if __name__ == "__main__":
    sys.exit(main())
