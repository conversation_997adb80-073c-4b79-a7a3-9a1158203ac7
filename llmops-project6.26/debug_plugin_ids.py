#!/usr/bin/env python3
"""
调试插件ID格式问题
"""

import requests
import json
import sys

def debug_plugin_ids():
    """调试插件ID格式"""
    
    # 配置
    base_url = "http://172.16.201.93:30080"
    auth_token = "Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJleHAiOjQ4MDk5MDk5OTEsImlhdCI6MTY1NjMwOTk5MSwicm9sZXMiOiJbXCJTT1BIT05fQURNSU5cIixcImFkbWluXCIsXCJST0xFX0FETUlOXCJdIiwic2NvcGUiOiJpbnRlcm5hbCIsInVzZXJuYW1lIjoidGhpbmdlciJ9.G9bUZ984g-8M-yBuUisrW9HtIK-yP0G-tD4DW9VQ7xAvmyMRaTOJ9DlucZj7KK_ynES1Qa96HZV_Wz5KlUUWtA"
    
    headers = {
        'Authorization': auth_token,
        'Content-Type': 'application/json'
    }
    
    print("🔍 调试插件ID格式问题")
    print("=" * 60)
    
    # 1. 获取插件列表，查看实际的plugin_id格式
    print("\n📋 步骤1: 获取插件列表")
    try:
        response = requests.get(f"{base_url}/api/v1/app/dify-plugins/plugins", headers=headers, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            plugins = data.get('plugins', [])
            print(f"✅ 成功获取插件列表，共 {len(plugins)} 个插件")
            
            if plugins:
                print("\n插件ID格式分析:")
                for i, plugin in enumerate(plugins):
                    plugin_id = plugin.get('plugin_id', 'Unknown')
                    name = plugin.get('name', 'Unknown')
                    unique_id = plugin.get('unique_identifier', 'Unknown')
                    source = plugin.get('source', 'Unknown')
                    
                    print(f"  {i+1}. 名称: {name}")
                    print(f"     plugin_id: '{plugin_id}'")
                    print(f"     unique_identifier: '{unique_id}'")
                    print(f"     source: {source}")
                    print(f"     ---")
                    
                    # 测试这个plugin_id是否能获取详情
                    print(f"     测试获取详情...")
                    try:
                        detail_response = requests.get(
                            f"{base_url}/api/v1/app/dify-plugins/plugins/{plugin_id}", 
                            headers=headers, 
                            timeout=10
                        )
                        print(f"     详情API状态码: {detail_response.status_code}")
                        if detail_response.status_code != 200:
                            print(f"     详情API错误: {detail_response.text[:100]}...")
                    except Exception as e:
                        print(f"     详情API异常: {e}")
                    
                    print()
                    
                    if i >= 4:  # 只测试前5个插件
                        print(f"     ... 还有 {len(plugins) - 5} 个插件")
                        break
            else:
                print("⚠️  插件列表为空")
        else:
            print(f"❌ 获取插件列表失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 2. 测试特定的插件ID
    print(f"\n🎯 步骤2: 测试特定插件ID")
    
    test_ids = [
        "google",
        "langgenius/google", 
        "langgenius/firecrawl",
        "firecrawl"
    ]
    
    for test_id in test_ids:
        print(f"\n测试插件ID: '{test_id}'")
        try:
            # 测试获取详情
            detail_response = requests.get(
                f"{base_url}/api/v1/app/dify-plugins/plugins/{test_id}", 
                headers=headers, 
                timeout=10
            )
            print(f"  详情API状态码: {detail_response.status_code}")
            
            if detail_response.status_code == 200:
                detail_data = detail_response.json()
                print(f"  ✅ 成功获取详情")
                print(f"  名称: {detail_data.get('name', 'Unknown')}")
            else:
                print(f"  ❌ 获取详情失败: {detail_response.text[:100]}...")
            
            # 测试获取授权表单
            auth_response = requests.get(
                f"{base_url}/api/v1/app/dify-plugins/plugins/{test_id}/auth/form", 
                headers=headers, 
                timeout=10
            )
            print(f"  授权表单API状态码: {auth_response.status_code}")
            
            if auth_response.status_code == 200:
                auth_data = auth_response.json()
                print(f"  ✅ 成功获取授权表单")
                fields = auth_data.get('fields', [])
                print(f"  授权字段数量: {len(fields)}")
            else:
                print(f"  ❌ 获取授权表单失败: {auth_response.text[:100]}...")
                
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")
    
    print(f"\n" + "=" * 60)
    print("🎯 分析结论:")
    print("1. 检查插件列表中实际的plugin_id格式")
    print("2. 确认哪种格式的plugin_id能成功获取详情")
    print("3. 找出marketplace插件的正确ID格式")
    print("4. 修复plugin_id匹配逻辑")

def main():
    """主函数"""
    debug_plugin_ids()
    return 0

if __name__ == "__main__":
    sys.exit(main())
