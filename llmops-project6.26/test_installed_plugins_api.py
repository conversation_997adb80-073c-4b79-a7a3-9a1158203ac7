#!/usr/bin/env python3
"""
测试获取已安装插件信息及授权状态的API
"""

import requests
import json
import sys

def test_agent_tool_api():
    """测试agent-tool-api的直接接口"""
    print("🔍 测试agent-tool-api直接接口...")
    
    url = "http://localhost:8080/v1/dify-plugins/installed"
    
    try:
        response = requests.get(url, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ agent-tool-api响应成功")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('success'):
                plugins = data.get('data', [])
                print(f"\n📊 统计信息:")
                print(f"总插件数: {len(plugins)}")
                
                auth_stats = {
                    'authorized': 0,
                    'unauthorized': 0,
                    'no_auth_required': 0
                }
                
                for plugin in plugins:
                    auth_status = plugin.get('auth_status', 'unknown')
                    if auth_status in auth_stats:
                        auth_stats[auth_status] += 1
                    
                    print(f"- {plugin.get('plugin_name', 'Unknown')}: {auth_status}")
                
                print(f"\n授权统计:")
                print(f"- 已授权: {auth_stats['authorized']}")
                print(f"- 未授权: {auth_stats['unauthorized']}")
                print(f"- 无需授权: {auth_stats['no_auth_required']}")
                
            else:
                print(f"❌ API返回失败: {data.get('message', 'Unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_applet_backend_api():
    """测试applet-backend的接口"""
    print("\n🔍 测试applet-backend接口...")
    
    url = "http://localhost:8081/api/v1/app/dify-plugins/installed"
    
    try:
        response = requests.get(url, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ applet-backend响应成功")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if isinstance(data, list):
                plugins = data
                print(f"\n📊 统计信息:")
                print(f"总插件数: {len(plugins)}")
                
                auth_stats = {
                    'authorized': 0,
                    'unauthorized': 0,
                    'no_auth_required': 0
                }
                
                for plugin in plugins:
                    auth_status = plugin.get('auth_status', 'unknown')
                    if auth_status in auth_stats:
                        auth_stats[auth_status] += 1
                    
                    print(f"- {plugin.get('plugin_name', 'Unknown')}: {auth_status}")
                
                print(f"\n授权统计:")
                print(f"- 已授权: {auth_stats['authorized']}")
                print(f"- 未授权: {auth_stats['unauthorized']}")
                print(f"- 无需授权: {auth_stats['no_auth_required']}")
                
            else:
                print(f"❌ 响应格式错误，期望数组，得到: {type(data)}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_dify_apis():
    """测试Dify原始API"""
    print("\n🔍 测试Dify原始API...")
    
    auth_token = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMGI2N2Y3ZjYtMjQ5OC00ZjM0LTk0ZDEtMmMwZmI0NDVmMDg0IiwiZXhwIjoxNzg0OTQ4NzE1LCJpc3MiOiJTRUxGX0hPU1RFRCIsInN1YiI6IkNvbnNvbGUgQVBJIFBhc3Nwb3J0In0.HWPgwNIp0ux1ii9AAcM7CSxKXiTQ3I2r63uhd8FWuxI"
    
    # 测试插件列表API
    print("📋 测试插件列表API...")
    try:
        url = "http://************/console/api/workspaces/current/plugin/list"
        headers = {
            "Authorization": auth_token,
            "Content-Type": "application/json"
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            data = response.json()
            plugins = data.get('plugins', [])
            print(f"✅ 获取到 {len(plugins)} 个插件")
            
            for plugin in plugins[:3]:  # 只显示前3个
                print(f"- {plugin.get('name', 'Unknown')}: {plugin.get('unique_identifier', 'No ID')}")
        else:
            print(f"❌ 插件列表API失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 插件列表API请求失败: {e}")
    
    # 测试工具提供者API
    print("\n🔧 测试工具提供者API...")
    try:
        url = "http://************/console/api/workspaces/current/tool-providers"
        headers = {
            "Authorization": auth_token,
            "Content-Type": "application/json"
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            providers = response.json()
            print(f"✅ 获取到 {len(providers)} 个工具提供者")
            
            authorized_count = sum(1 for p in providers if p.get('is_team_authorization', False))
            print(f"已授权: {authorized_count}, 未授权: {len(providers) - authorized_count}")
            
            for provider in providers[:3]:  # 只显示前3个
                name = provider.get('name', 'Unknown')
                is_auth = provider.get('is_team_authorization', False)
                print(f"- {name}: {'已授权' if is_auth else '未授权'}")
        else:
            print(f"❌ 工具提供者API失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 工具提供者API请求失败: {e}")

def main():
    """主函数"""
    print("🚀 开始测试获取已安装插件信息及授权状态的API")
    print("=" * 60)
    
    # 测试agent-tool-api
    test_agent_tool_api()
    
    # 测试applet-backend
    test_applet_backend_api()
    
    # 测试Dify原始API
    test_dify_apis()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成")

if __name__ == "__main__":
    main()
